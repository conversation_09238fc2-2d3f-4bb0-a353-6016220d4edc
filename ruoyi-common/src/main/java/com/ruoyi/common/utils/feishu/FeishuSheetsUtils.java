package com.ruoyi.common.utils.feishu;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.sheets.v3.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;

import java.nio.charset.StandardCharsets;
import java.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 飞书电子表格操作工具类
 * 基于官方SDK示例编写
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Component
public class FeishuSheetsUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FeishuSheetsUtils.class);
    
    @Value("${feishu.app-id}")
    private String appId;
    
    @Value("${feishu.app-secret}")
    private String appSecret;
    
    private ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取飞书客户端
     */
    private Client getClient() {
        return Client.newBuilder(appId, appSecret).build();
    }
    
    /**
     * 创建电子表格
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet/create
     * 
     * @param title 表格标题
     * @param folderToken 文件夹token（可选）
     * @return 创建的电子表格信息，如果失败返回null
     */
    public Spreadsheet createSpreadsheet(String title, String folderToken) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            Spreadsheet.Builder spreadsheetBuilder = Spreadsheet.newBuilder()
                    .title(title);
            
            // 如果提供了文件夹token，则设置
            if (folderToken != null && !folderToken.isEmpty()) {
                spreadsheetBuilder.folderToken(folderToken);
            }
            
            CreateSpreadsheetReq req = CreateSpreadsheetReq.newBuilder()
                    .spreadsheet(spreadsheetBuilder.build())
                    .build();
            
            // 发起请求
            CreateSpreadsheetResp resp = client.sheets().v3().spreadsheet().create(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("创建电子表格失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("创建电子表格成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData().getSpreadsheet();
            
        } catch (Exception e) {
            logger.error("创建电子表格异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 创建电子表格（简化版本，不指定文件夹）
     * 
     * @param title 表格标题
     * @return 创建的电子表格信息，如果失败返回null
     */
    public Spreadsheet createSpreadsheet(String title) {
        return createSpreadsheet(title, null);
    }
    
    /**
     * 修改电子表格属性
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet/patch
     * 
     * @param spreadsheetToken 电子表格token
     * @param title 新的表格标题
     * @return 修改后的电子表格信息，如果失败返回null
     */
    public Spreadsheet patchSpreadsheet(String spreadsheetToken, String title) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            PatchSpreadsheetReq req = PatchSpreadsheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .updateSpreadsheetProperties(UpdateSpreadsheetProperties.newBuilder()
                            .title(title)
                            .build())
                    .build();
            
            // 发起请求
            PatchSpreadsheetResp resp = client.sheets().v3().spreadsheet().patch(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("修改电子表格属性失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("修改电子表格属性成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            // patch接口成功时返回空数据，表示修改成功
            return new Spreadsheet.Builder().title(title).build();
            
        } catch (Exception e) {
            logger.error("修改电子表格属性异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取电子表格信息
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet/get
     * 
     * @param spreadsheetToken 电子表格token
     * @param userIdType 用户ID类型（open_id, union_id, user_id）
     * @return 电子表格信息，如果失败返回null
     */
    public GetSpreadsheet getSpreadsheet(String spreadsheetToken, String userIdType) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            GetSpreadsheetReq req = GetSpreadsheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .userIdType(userIdType)
                    .build();
            
            // 发起请求
            GetSpreadsheetResp resp = client.sheets().v3().spreadsheet().get(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("获取电子表格信息失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("获取电子表格信息成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData().getSpreadsheet();
            
        } catch (Exception e) {
            logger.error("获取电子表格信息异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取电子表格信息（使用默认用户ID类型：open_id）
     * 
     * @param spreadsheetToken 电子表格token
     * @return 电子表格信息，如果失败返回null
     */
    public GetSpreadsheet getSpreadsheet(String spreadsheetToken) {
        return getSpreadsheet(spreadsheetToken, "open_id");
    }
    
    // ==================== 工作表操作相关方法（基于HTTP调用） ====================
    
    private RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 获取飞书访问凭证（tenant_access_token）
     * 
     * @return 访问凭证，如果失败返回null
     */
    private String getTenantAccessToken() {
        try {
            String url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal/";
            
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("app_id", appId);
            requestBody.put("app_secret", appSecret);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    return (String) body.get("tenant_access_token");
                } else {
                    logger.error("获取tenant_access_token失败: {}", body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("获取tenant_access_token异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 操作工作表（支持增加、复制、删除工作表）
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/operate-sheets
     * 
     * @param spreadsheetToken 电子表格token
     * @param requests 操作请求列表
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> operateSheets(String spreadsheetToken, List<Map<String, Object>> requests) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/sheets_batch_update";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("requests", requests);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("操作工作表成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("操作工作表失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("操作工作表异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 添加工作表
     * 
     * @param spreadsheetToken 电子表格token
     * @param title 工作表标题
     * @param index 工作表位置索引（可选，从0开始）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> addSheet(String spreadsheetToken, String title, Integer index) {
        Map<String, Object> addSheetRequest = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        properties.put("title", title);
        if (index != null) {
            properties.put("index", index);
        }
        
        Map<String, Object> addSheet = new HashMap<>();
        addSheet.put("properties", properties);
        
        addSheetRequest.put("addSheet", addSheet);
        
        List<Map<String, Object>> requests = Collections.singletonList(addSheetRequest);
        return operateSheets(spreadsheetToken, requests);
    }
    
    /**
     * 添加工作表（简化版本，不指定索引）
     * 
     * @param spreadsheetToken 电子表格token
     * @param title 工作表标题
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> addSheet(String spreadsheetToken, String title) {
        return addSheet(spreadsheetToken, title, null);
    }
    
    /**
     * 复制工作表
     * 
     * @param spreadsheetToken 电子表格token
     * @param sourceSheetId 源工作表ID
     * @param destinationTitle 目标工作表标题
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> copySheet(String spreadsheetToken, String sourceSheetId, String destinationTitle) {
        Map<String, Object> copySheetRequest = new HashMap<>();
        
        Map<String, Object> source = new HashMap<>();
        source.put("sheetId", sourceSheetId);
        
        Map<String, Object> destination = new HashMap<>();
        destination.put("title", destinationTitle);
        
        Map<String, Object> copySheet = new HashMap<>();
        copySheet.put("source", source);
        copySheet.put("destination", destination);
        
        copySheetRequest.put("copySheet", copySheet);
        
        List<Map<String, Object>> requests = Collections.singletonList(copySheetRequest);
        return operateSheets(spreadsheetToken, requests);
    }
    
    /**
     * 删除工作表
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> deleteSheet(String spreadsheetToken, String sheetId) {
        Map<String, Object> deleteSheetRequest = new HashMap<>();
        
        Map<String, Object> deleteSheet = new HashMap<>();
        deleteSheet.put("sheetId", sheetId);
        
        deleteSheetRequest.put("deleteSheet", deleteSheet);
        
        List<Map<String, Object>> requests = Collections.singletonList(deleteSheetRequest);
        return operateSheets(spreadsheetToken, requests);
    }
    
    /**
     * 更新工作表属性
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/update-sheet-properties
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param properties 更新的属性
     * @param userIdType 用户ID类型（可选，默认为open_id）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetProperties(String spreadsheetToken, String sheetId, 
                                                   Map<String, Object> properties, String userIdType) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/sheets_batch_update";
            if (userIdType != null && !userIdType.isEmpty()) {
                url += "?user_id_type=" + userIdType;
            }
            
            // 构建更新属性
            Map<String, Object> sheetProperties = new HashMap<>();
            sheetProperties.put("sheetId", sheetId);
            sheetProperties.putAll(properties);
            
            Map<String, Object> updateSheet = new HashMap<>();
            updateSheet.put("properties", sheetProperties);
            
            Map<String, Object> updateSheetRequest = new HashMap<>();
            updateSheetRequest.put("updateSheet", updateSheet);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("requests", Collections.singletonList(updateSheetRequest));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("更新工作表属性成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("更新工作表属性失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("更新工作表属性异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 更新工作表属性（使用默认用户ID类型：open_id）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param properties 更新的属性
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetProperties(String spreadsheetToken, String sheetId, 
                                                   Map<String, Object> properties) {
        return updateSheetProperties(spreadsheetToken, sheetId, properties, "open_id");
    }
    
    /**
     * 更新工作表标题
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param title 新标题
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetTitle(String spreadsheetToken, String sheetId, String title) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("title", title);
        return updateSheetProperties(spreadsheetToken, sheetId, properties);
    }
    
    /**
     * 更新工作表位置
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param index 新位置索引（从0开始）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetIndex(String spreadsheetToken, String sheetId, Integer index) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("index", index);
        return updateSheetProperties(spreadsheetToken, sheetId, properties);
    }
    
    /**
     * 设置工作表隐藏状态
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param hidden 是否隐藏
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetHidden(String spreadsheetToken, String sheetId, Boolean hidden) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("hidden", hidden);
        return updateSheetProperties(spreadsheetToken, sheetId, properties);
    }
    
    /**
     * 设置工作表冻结
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param frozenColCount 冻结列数
     * @param frozenRowCount 冻结行数
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetFrozen(String spreadsheetToken, String sheetId, 
                                                Integer frozenColCount, Integer frozenRowCount) {
        Map<String, Object> properties = new HashMap<>();
        if (frozenColCount != null) {
            properties.put("frozenColCount", frozenColCount);
        }
        if (frozenRowCount != null) {
            properties.put("frozenRowCount", frozenRowCount);
        }
        return updateSheetProperties(spreadsheetToken, sheetId, properties);
    }
    
    /**
     * 设置工作表保护
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param lock 锁定类型（LOCK/UNLOCK）
     * @param lockInfo 保护信息说明
     * @param userIds 有权限的用户ID列表
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateSheetProtection(String spreadsheetToken, String sheetId, 
                                                    String lock, String lockInfo, List<String> userIds) {
        Map<String, Object> protect = new HashMap<>();
        protect.put("lock", lock);
        if (lockInfo != null) {
            protect.put("lockInfo", lockInfo);
        }
        if (userIds != null && !userIds.isEmpty()) {
            protect.put("userIDs", userIds);
        }
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("protect", protect);
        return updateSheetProperties(spreadsheetToken, sheetId, properties);
    }
    
    /**
     * 获取工作表列表
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/query
     * 
     * @param spreadsheetToken 电子表格token
     * @return 工作表列表，如果失败返回null
     */
    public QuerySpreadsheetSheetRespBody querySpreadsheetSheets(String spreadsheetToken) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            QuerySpreadsheetSheetReq req = QuerySpreadsheetSheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .build();
            
            // 发起请求
            QuerySpreadsheetSheetResp resp = client.sheets().v3().spreadsheetSheet().query(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("获取工作表列表失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("获取工作表列表成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("获取工作表列表异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 查询单个工作表
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet/get
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 工作表信息，如果失败返回null
     */
    public GetSpreadsheetSheetRespBody getSpreadsheetSheet(String spreadsheetToken, String sheetId) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            GetSpreadsheetSheetReq req = GetSpreadsheetSheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .build();
            
            // 发起请求
            GetSpreadsheetSheetResp resp = client.sheets().v3().spreadsheetSheet().get(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("查询工作表失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("查询工作表成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("查询工作表异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    // ==================== 行列操作相关方法（基于HTTP调用） ====================
    
    /**
     * 增加行列
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/sheet-rowcol/add-rows-or-columns
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param length 增加的行数或列数（最多5000）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> addRowsOrColumns(String spreadsheetToken, String sheetId, 
                                              String majorDimension, Integer length) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dimension_range";
            
            // 构建dimension对象
            Map<String, Object> dimension = new HashMap<>();
            dimension.put("sheetId", sheetId);
            dimension.put("majorDimension", majorDimension);
            dimension.put("length", length);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dimension", dimension);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("增加行列成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("增加行列失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("增加行列异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 增加行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param rowCount 增加的行数
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> addRows(String spreadsheetToken, String sheetId, Integer rowCount) {
        return addRowsOrColumns(spreadsheetToken, sheetId, "ROWS", rowCount);
    }
    
    /**
     * 增加列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param columnCount 增加的列数
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> addColumns(String spreadsheetToken, String sheetId, Integer columnCount) {
        return addRowsOrColumns(spreadsheetToken, sheetId, "COLUMNS", columnCount);
    }
    
    /**
     * 插入行列
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/sheet-rowcol/insert-rows-or-columns
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param inheritStyle 样式继承类型（可选：BEFORE/AFTER/null）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertRowsOrColumns(String spreadsheetToken, String sheetId, 
                                                 String majorDimension, Integer startIndex, 
                                                 Integer endIndex, String inheritStyle) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/insert_dimension_range";
            
            // 构建dimension对象
            Map<String, Object> dimension = new HashMap<>();
            dimension.put("sheetId", sheetId);
            dimension.put("majorDimension", majorDimension);
            dimension.put("startIndex", startIndex);
            dimension.put("endIndex", endIndex);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dimension", dimension);
            
            // 如果指定了样式继承，则添加到请求体中
            if (inheritStyle != null && !inheritStyle.isEmpty()) {
                requestBody.put("inheritStyle", inheritStyle);
            }
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("插入行列成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("插入行列失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("插入行列异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 插入行列（不继承样式）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertRowsOrColumns(String spreadsheetToken, String sheetId, 
                                                 String majorDimension, Integer startIndex, Integer endIndex) {
        return insertRowsOrColumns(spreadsheetToken, sheetId, majorDimension, startIndex, endIndex, null);
    }
    
    /**
     * 插入行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param inheritStyle 样式继承类型（可选：BEFORE/AFTER/null）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertRows(String spreadsheetToken, String sheetId, 
                                        Integer startIndex, Integer endIndex, String inheritStyle) {
        return insertRowsOrColumns(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex, inheritStyle);
    }
    
    /**
     * 插入行（不继承样式）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertRows(String spreadsheetToken, String sheetId, 
                                        Integer startIndex, Integer endIndex) {
        return insertRows(spreadsheetToken, sheetId, startIndex, endIndex, null);
    }
    
    /**
     * 插入列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param inheritStyle 样式继承类型（可选：BEFORE/AFTER/null）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertColumns(String spreadsheetToken, String sheetId, 
                                           Integer startIndex, Integer endIndex, String inheritStyle) {
        return insertRowsOrColumns(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex, inheritStyle);
    }
    
    /**
     * 插入列（不继承样式）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> insertColumns(String spreadsheetToken, String sheetId, 
                                           Integer startIndex, Integer endIndex) {
        return insertColumns(spreadsheetToken, sheetId, startIndex, endIndex, null);
    }
    
    /**
     * 更新行列属性
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/sheet-rowcol/update-rows-or-columns
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param properties 要更新的属性（如visible、fixedSize等）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> updateRowsOrColumns(String spreadsheetToken, String sheetId, 
                                                 String majorDimension, Integer startIndex, 
                                                 Integer endIndex, Map<String, Object> properties) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dimension_range";
            
            // 构建dimension对象
            Map<String, Object> dimension = new HashMap<>();
            dimension.put("sheetId", sheetId);
            dimension.put("majorDimension", majorDimension);
            dimension.put("startIndex", startIndex);
            dimension.put("endIndex", endIndex);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dimension", dimension);
            requestBody.put("dimensionProperties", properties);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 注意：此接口使用PUT方法
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.PUT, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("更新行列属性成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("更新行列属性失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("更新行列属性异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 设置行列可见性
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param visible 是否可见
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> setRowsOrColumnsVisible(String spreadsheetToken, String sheetId, 
                                                     String majorDimension, Integer startIndex, 
                                                     Integer endIndex, Boolean visible) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("visible", visible);
        return updateRowsOrColumns(spreadsheetToken, sheetId, majorDimension, startIndex, endIndex, properties);
    }
    
    /**
     * 设置行列尺寸
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param fixedSize 固定尺寸（行高或列宽）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> setRowsOrColumnsSize(String spreadsheetToken, String sheetId, 
                                                  String majorDimension, Integer startIndex, 
                                                  Integer endIndex, Integer fixedSize) {
        Map<String, Object> properties = new HashMap<>();
        properties.put("fixedSize", fixedSize);
        return updateRowsOrColumns(spreadsheetToken, sheetId, majorDimension, startIndex, endIndex, properties);
    }
    
    /**
     * 隐藏行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> hideRows(String spreadsheetToken, String sheetId, 
                                      Integer startIndex, Integer endIndex) {
        return setRowsOrColumnsVisible(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex, false);
    }
    
    /**
     * 显示行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> showRows(String spreadsheetToken, String sheetId, 
                                      Integer startIndex, Integer endIndex) {
        return setRowsOrColumnsVisible(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex, true);
    }
    
    /**
     * 隐藏列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> hideColumns(String spreadsheetToken, String sheetId, 
                                         Integer startIndex, Integer endIndex) {
        return setRowsOrColumnsVisible(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex, false);
    }
    
    /**
     * 显示列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> showColumns(String spreadsheetToken, String sheetId, 
                                         Integer startIndex, Integer endIndex) {
        return setRowsOrColumnsVisible(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex, true);
    }
    
    /**
     * 设置行高
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param height 行高
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> setRowHeight(String spreadsheetToken, String sheetId, 
                                          Integer startIndex, Integer endIndex, Integer height) {
        return setRowsOrColumnsSize(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex, height);
    }
    
    /**
     * 设置列宽
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始位置（从0开始计数）
     * @param endIndex 结束位置（从0开始计数）
     * @param width 列宽
     * @return 操作结果，如果失败返回null
     */
    public Map<String, Object> setColumnWidth(String spreadsheetToken, String sheetId, 
                                            Integer startIndex, Integer endIndex, Integer width) {
        return setRowsOrColumnsSize(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex, width);
    }
    
    /**
     * 移动行列
     * 参考官方示例：https://open.feishu.cn/document/server-docs/docs/sheets-v3/sheet-rowcol/move_dimension
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 维度类型（ROWS或COLUMNS）
     * @param startIndex 源起始位置（从0开始计数）
     * @param endIndex 源结束位置（从0开始计数）
     * @param destinationIndex 目标位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Object moveDimension(String spreadsheetToken, String sheetId, 
                              String majorDimension, Integer startIndex, 
                              Integer endIndex, Integer destinationIndex) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            MoveDimensionSpreadsheetSheetReq req = MoveDimensionSpreadsheetSheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .moveDimension(MoveDimension.newBuilder()
                            .source(Dimension.newBuilder()
                                    .majorDimension(majorDimension)
                                    .startIndex(startIndex)
                                    .endIndex(endIndex)
                                    .build())
                            .destinationIndex(destinationIndex)
                            .build())
                    .build();
            
            // 发起请求
            MoveDimensionSpreadsheetSheetResp resp = client.sheets().v3().spreadsheetSheet().moveDimension(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("移动行列失败 - code:{}, msg:{}, reqId:{}, resp:{}", 
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), 
                        Jsons.createGSON(true, false).toJson(JsonParser.parseString(
                                new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8))));
                return null;
            }
            
            // 业务数据处理
            logger.info("移动行列成功: {}", Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("移动行列异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 移动行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 源起始位置（从0开始计数）
     * @param endIndex 源结束位置（从0开始计数）
     * @param destinationIndex 目标位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Object moveRows(String spreadsheetToken, String sheetId, 
                         Integer startIndex, Integer endIndex, 
                         Integer destinationIndex) {
        return moveDimension(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex, destinationIndex);
    }
    
    /**
     * 移动列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 源起始位置（从0开始计数）
     * @param endIndex 源结束位置（从0开始计数）
     * @param destinationIndex 目标位置（从0开始计数）
     * @return 操作结果，如果失败返回null
     */
    public Object moveColumns(String spreadsheetToken, String sheetId, 
                            Integer startIndex, Integer endIndex, 
                            Integer destinationIndex) {
        return moveDimension(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex, destinationIndex);
    }
    
    // ==================== 删除行列相关方法（基于HTTP调用） ====================
    
    /**
     * 删除行或列
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/sheet-rowcol/-delete-rows-or-columns
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param majorDimension 操作维度：ROWS（行）或 COLUMNS（列）
     * @param startIndex 起始索引（从1开始计数，包含）
     * @param endIndex 结束索引（从1开始计数，包含）
     * @return 删除结果，如果失败返回null
     */
    public Map<String, Object> deleteRowsOrColumns(String spreadsheetToken, String sheetId, 
                                                   String majorDimension, Integer startIndex, Integer endIndex) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            // 构建URL（使用v2版本的API）
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/dimension_range", 
                                     spreadsheetToken);
            
            // 构建dimension对象（根据官方文档要求）
            Map<String, Object> dimension = new HashMap<>();
            dimension.put("sheetId", sheetId);
            dimension.put("majorDimension", majorDimension);
            dimension.put("startIndex", startIndex);  // 从1开始计数
            dimension.put("endIndex", endIndex);      // 从1开始计数
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dimension", dimension);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.DELETE, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("删除{}成功，范围：第{}到第{}{}（共{}{}）", 
                              majorDimension.equals("ROWS") ? "行" : "列", 
                              startIndex, endIndex,
                              majorDimension.equals("ROWS") ? "行" : "列",
                              endIndex - startIndex + 1,
                              majorDimension.equals("ROWS") ? "行" : "列");
                    return body;
                } else {
                    logger.error("删除{}失败: {}", majorDimension.equals("ROWS") ? "行" : "列", body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("删除{}异常: {}", majorDimension.equals("ROWS") ? "行" : "列", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 删除行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始行索引（包含）
     * @param endIndex 结束行索引（包含）
     * @return 删除结果，如果失败返回null
     */
    public Map<String, Object> deleteRows(String spreadsheetToken, String sheetId, 
                                         Integer startIndex, Integer endIndex) {
        return deleteRowsOrColumns(spreadsheetToken, sheetId, "ROWS", startIndex, endIndex);
    }
    
    /**
     * 删除单行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param rowIndex 行索引
     * @return 删除结果，如果失败返回null
     */
    public Map<String, Object> deleteRow(String spreadsheetToken, String sheetId, Integer rowIndex) {
        return deleteRows(spreadsheetToken, sheetId, rowIndex, rowIndex);
    }
    
    /**
     * 删除列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startIndex 起始列索引（包含）
     * @param endIndex 结束列索引（包含）
     * @return 删除结果，如果失败返回null
     */
    public Map<String, Object> deleteColumns(String spreadsheetToken, String sheetId, 
                                            Integer startIndex, Integer endIndex) {
        return deleteRowsOrColumns(spreadsheetToken, sheetId, "COLUMNS", startIndex, endIndex);
    }
    
    /**
     * 删除单列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param columnIndex 列索引
     * @return 删除结果，如果失败返回null
     */
    public Map<String, Object> deleteColumn(String spreadsheetToken, String sheetId, Integer columnIndex) {
        return deleteColumns(spreadsheetToken, sheetId, columnIndex, columnIndex);
    }
    
    // ==================== 单元格合并拆分相关方法（基于官方API文档） ====================
    
    /**
     * 合并单元格
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/merge-cells
     * API地址：POST https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/merge_cells
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 合并范围，如"A1:C3"
     * @param mergeType 合并类型：MERGE_ALL（合并所有）、MERGE_ROWS（按行合并）、MERGE_COLUMNS（按列合并）
     * @return 合并结果，如果失败返回null
     */
    public Map<String, Object> mergeCells(String spreadsheetToken, String sheetId, String range, String mergeType) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/merge_cells", 
                                     spreadsheetToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("range", sheetId + "!" + range);
            requestBody.put("mergeType", mergeType);
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> result = objectMapper.readValue(response.getBody(), 
                    new TypeReference<Map<String, Object>>() {});
                logger.info("合并单元格成功: {} 范围: {}", mergeType, range);
                return result;
            } else {
                logger.error("合并单元格失败，状态码: {}, 响应: {}", 
                    response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            logger.error("合并单元格时发生异常", e);
            return null;
        }
    }
    
    /**
     * 合并单元格（默认全部合并）
     */
    public Map<String, Object> mergeCells(String spreadsheetToken, String sheetId, String range) {
        return mergeCells(spreadsheetToken, sheetId, range, "MERGE_ALL");
    }
    
    /**
     * 按行合并单元格
     */
    public Map<String, Object> mergeCellsByRows(String spreadsheetToken, String sheetId, String range) {
        return mergeCells(spreadsheetToken, sheetId, range, "MERGE_ROWS");
    }
    
    /**
     * 按列合并单元格
     */
    public Map<String, Object> mergeCellsByColumns(String spreadsheetToken, String sheetId, String range) {
        return mergeCells(spreadsheetToken, sheetId, range, "MERGE_COLUMNS");
    }
    
    /**
     * 拆分单元格（取消合并）
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/split-cells
     * API地址：POST https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/unmerge_cells
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 拆分范围，如"A1:C3"
     * @return 拆分结果，如果失败返回null
     */
    public Map<String, Object> splitCells(String spreadsheetToken, String sheetId, String range) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/unmerge_cells", 
                                     spreadsheetToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("range", sheetId + "!" + range);
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> result = objectMapper.readValue(response.getBody(), 
                    new TypeReference<Map<String, Object>>() {});
                logger.info("拆分单元格成功，范围: {}", range);
                return result;
            } else {
                logger.error("拆分单元格失败，状态码: {}, 响应: {}", 
                    response.getStatusCode(), response.getBody());
                return null;
            }
        } catch (Exception e) {
            logger.error("拆分单元格时发生异常", e);
            return null;
        }
    }
    
    /**
     * 取消合并单元格（splitCells的别名）
     */
    public Map<String, Object> unmergeCells(String spreadsheetToken, String sheetId, String range) {
        return splitCells(spreadsheetToken, sheetId, range);
    }
    
    // ==================== 单元格查找相关方法（基于v3 SDK） ====================
    
    /**
     * 查找单元格
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/find
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 查找范围，如"A1:C5"
     * @param findText 要查找的文本
     * @param matchCase 是否区分大小写
     * @param matchEntireCell 是否完全匹配单元格
     * @param searchByRegex 是否使用正则表达式搜索
     * @param includeFormulas 是否在公式中搜索
     * @return 查找结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetRespBody findCells(
            String spreadsheetToken, String sheetId, String range, String findText,
            boolean matchCase, boolean matchEntireCell, boolean searchByRegex, boolean includeFormulas) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetReq req = 
                com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .find(com.lark.oapi.service.sheets.v3.model.Find.newBuilder()
                        .findCondition(com.lark.oapi.service.sheets.v3.model.FindCondition.newBuilder()
                            .range(range)
                            .matchCase(matchCase)
                            .matchEntireCell(matchEntireCell)
                            .searchByRegex(searchByRegex)
                            .includeFormulas(includeFormulas)
                            .build())
                        .find(findText)
                        .build())
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetResp resp = 
                client.sheets().v3().spreadsheetSheet().find(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("查找单元格失败: code={}, msg={}, reqId={}", 
                    resp.getCode(), resp.getMsg(), resp.getRequestId());
                return null;
            }
            
            // 业务数据处理
            logger.info("查找单元格成功: {}", resp.getData());
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("查找单元格异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 查找单元格（简化版本，使用默认参数）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 查找范围，如"A1:C5"
     * @param findText 要查找的文本
     * @return 查找结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetRespBody findCells(
            String spreadsheetToken, String sheetId, String range, String findText) {
        return findCells(spreadsheetToken, sheetId, range, findText, 
                        true, false, false, false);
    }
    
    /**
     * 在整个工作表中查找单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param findText 要查找的文本
     * @return 查找结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetRespBody findCellsInSheet(
            String spreadsheetToken, String sheetId, String findText) {
        // 使用整个工作表范围
        String range = sheetId + "!A1:ZZ1000";
        return findCells(spreadsheetToken, sheetId, range, findText);
    }
    
    /**
     * 使用正则表达式查找单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 查找范围，如"A1:C5"
     * @param regexPattern 正则表达式模式
     * @return 查找结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetRespBody findCellsByRegex(
            String spreadsheetToken, String sheetId, String range, String regexPattern) {
        return findCells(spreadsheetToken, sheetId, range, regexPattern, 
                        false, false, true, false);
    }
    
    /**
     * 完全匹配查找单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 查找范围，如"A1:C5"
     * @param exactText 要完全匹配的文本
     * @return 查找结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.FindSpreadsheetSheetRespBody findCellsExact(
            String spreadsheetToken, String sheetId, String range, String exactText) {
        return findCells(spreadsheetToken, sheetId, range, exactText, 
                        true, true, false, false);
    }
    
    // ==================== 单元格替换相关方法（基于v3 SDK） ====================
    
    /**
     * 替换单元格
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/replace
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 替换范围，如"A1:C5"
     * @param findText 要查找的文本
     * @param replacement 替换为的文本
     * @param matchCase 是否区分大小写
     * @param matchEntireCell 是否完全匹配单元格
     * @param searchByRegex 是否使用正则表达式搜索
     * @param includeFormulas 是否在公式中搜索
     * @return 替换结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetRespBody replaceCells(
            String spreadsheetToken, String sheetId, String range, String findText, String replacement,
            boolean matchCase, boolean matchEntireCell, boolean searchByRegex, boolean includeFormulas) {
        try {
            // 构建client
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetReq req = 
                com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .replace(com.lark.oapi.service.sheets.v3.model.Replace.newBuilder()
                        .findCondition(com.lark.oapi.service.sheets.v3.model.FindCondition.newBuilder()
                            .range(range)
                            .matchCase(matchCase)
                            .matchEntireCell(matchEntireCell)
                            .searchByRegex(searchByRegex)
                            .includeFormulas(includeFormulas)
                            .build())
                        .find(findText)
                        .replacement(replacement)
                        .build())
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetResp resp = 
                client.sheets().v3().spreadsheetSheet().replace(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("替换单元格失败: code={}, msg={}, reqId={}", 
                    resp.getCode(), resp.getMsg(), resp.getRequestId());
                return null;
            }
            
            // 业务数据处理
            logger.info("替换单元格成功: {}", resp.getData());
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("替换单元格异常: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 替换单元格（简化版本，使用默认参数）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 替换范围，如"A1:C5"
     * @param findText 要查找的文本
     * @param replacement 替换为的文本
     * @return 替换结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetRespBody replaceCells(
            String spreadsheetToken, String sheetId, String range, String findText, String replacement) {
        return replaceCells(spreadsheetToken, sheetId, range, findText, replacement, 
                           true, false, false, false);
    }
    
    /**
     * 在整个工作表中替换单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param findText 要查找的文本
     * @param replacement 替换为的文本
     * @return 替换结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetRespBody replaceCellsInSheet(
            String spreadsheetToken, String sheetId, String findText, String replacement) {
        // 使用整个工作表范围
        String range = sheetId + "!A1:ZZ1000";
        return replaceCells(spreadsheetToken, sheetId, range, findText, replacement);
    }
    
    /**
     * 使用正则表达式替换单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 替换范围，如"A1:C5"
     * @param regexPattern 正则表达式模式
     * @param replacement 替换为的文本
     * @return 替换结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetRespBody replaceCellsByRegex(
            String spreadsheetToken, String sheetId, String range, String regexPattern, String replacement) {
        return replaceCells(spreadsheetToken, sheetId, range, regexPattern, replacement, 
                           false, false, true, false);
    }
    
    /**
     * 完全匹配替换单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 替换范围，如"A1:C5"
     * @param exactText 要完全匹配的文本
     * @param replacement 替换为的文本
     * @return 替换结果，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.ReplaceSpreadsheetSheetRespBody replaceCellsExact(
            String spreadsheetToken, String sheetId, String range, String exactText, String replacement) {
        return replaceCells(spreadsheetToken, sheetId, range, exactText, replacement, 
                           true, true, false, false);
    }
    
    // ==================== 单元格样式相关方法（基于v2 HTTP） ====================
    
    /**
     * 设置单元格样式
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/set-cell-style
     * API地址：PUT https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/style
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param styleProperties 样式属性
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setCellStyle(String spreadsheetToken, String range, Map<String, Object> styleProperties) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/style", 
                                     spreadsheetToken);
            
            // 构建appendStyle对象
            Map<String, Object> appendStyle = new HashMap<>();
            appendStyle.put("range", range);
            appendStyle.put("style", styleProperties);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("appendStyle", appendStyle);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.PUT, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("设置单元格样式成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("设置单元格样式失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("设置单元格样式异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 设置字体样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param bold 是否加粗
     * @param italic 是否斜体
     * @param fontSize 字体大小，如"12pt/1.5"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setFontStyle(String spreadsheetToken, String range, 
                                           Boolean bold, Boolean italic, String fontSize) {
        Map<String, Object> style = new HashMap<>();
        
        Map<String, Object> font = new HashMap<>();
        if (bold != null) font.put("bold", bold);
        if (italic != null) font.put("italic", italic);
        if (fontSize != null) font.put("fontSize", fontSize);
        font.put("clean", false);
        
        style.put("font", font);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    /**
     * 设置单元格背景色
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param backColor 背景色，如"#ff0000"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setBackgroundColor(String spreadsheetToken, String range, String backColor) {
        Map<String, Object> style = new HashMap<>();
        style.put("backColor", backColor);
        style.put("clean", false);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    /**
     * 设置单元格前景色（文字颜色）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param foreColor 前景色，如"#000000"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setForegroundColor(String spreadsheetToken, String range, String foreColor) {
        Map<String, Object> style = new HashMap<>();
        style.put("foreColor", foreColor);
        style.put("clean", false);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    /**
     * 设置单元格对齐方式
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param hAlign 水平对齐：0-左对齐, 1-居中, 2-右对齐
     * @param vAlign 垂直对齐：0-顶部对齐, 1-居中, 2-底部对齐
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setAlignment(String spreadsheetToken, String range, Integer hAlign, Integer vAlign) {
        Map<String, Object> style = new HashMap<>();
        if (hAlign != null) style.put("hAlign", hAlign);
        if (vAlign != null) style.put("vAlign", vAlign);
        style.put("clean", false);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    /**
     * 设置单元格边框
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param borderType 边框类型，如"FULL_BORDER"
     * @param borderColor 边框颜色，如"#000000"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setBorder(String spreadsheetToken, String range, String borderType, String borderColor) {
        Map<String, Object> style = new HashMap<>();
        style.put("borderType", borderType);
        style.put("borderColor", borderColor);
        style.put("clean", false);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    /**
     * 设置完整的单元格样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 样式范围，如"sheetId!A1:C3"
     * @param bold 是否加粗
     * @param italic 是否斜体
     * @param fontSize 字体大小
     * @param foreColor 前景色
     * @param backColor 背景色
     * @param hAlign 水平对齐
     * @param vAlign 垂直对齐
     * @param borderType 边框类型
     * @param borderColor 边框颜色
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> setCompleteStyle(String spreadsheetToken, String range,
                                               Boolean bold, Boolean italic, String fontSize,
                                               String foreColor, String backColor,
                                               Integer hAlign, Integer vAlign,
                                               String borderType, String borderColor) {
        Map<String, Object> style = new HashMap<>();
        
        // 字体设置
        if (bold != null || italic != null || fontSize != null) {
            Map<String, Object> font = new HashMap<>();
            if (bold != null) font.put("bold", bold);
            if (italic != null) font.put("italic", italic);
            if (fontSize != null) font.put("fontSize", fontSize);
            font.put("clean", false);
            style.put("font", font);
        }
        
        // 颜色设置
        if (foreColor != null) style.put("foreColor", foreColor);
        if (backColor != null) style.put("backColor", backColor);
        
        // 对齐设置
        if (hAlign != null) style.put("hAlign", hAlign);
        if (vAlign != null) style.put("vAlign", vAlign);
        
        // 边框设置
        if (borderType != null) style.put("borderType", borderType);
        if (borderColor != null) style.put("borderColor", borderColor);
        
        style.put("clean", false);
        
        return setCellStyle(spreadsheetToken, range, style);
    }
    
    // ==================== 批量设置单元格样式相关方法（基于v2 HTTP） ====================
    
    /**
     * 批量设置单元格样式
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/batch-set-cell-style
     * API地址：PUT https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/styles_batch_update
     * 
     * @param spreadsheetToken 电子表格token
     * @param batchStyleData 批量样式数据列表
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetCellStyle(String spreadsheetToken, List<Map<String, Object>> batchStyleData) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/styles_batch_update", 
                                     spreadsheetToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("data", batchStyleData);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.PUT, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("批量设置单元格样式成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("批量设置单元格样式失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("批量设置单元格样式异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 创建批量样式数据项
     * 
     * @param ranges 范围列表，如["sheetId!A1:C3", "sheetId!E1:F3"]
     * @param styleProperties 样式属性
     * @return 批量样式数据项
     */
    public Map<String, Object> createBatchStyleItem(List<String> ranges, Map<String, Object> styleProperties) {
        Map<String, Object> item = new HashMap<>();
        item.put("ranges", ranges);
        item.put("style", styleProperties);
        return item;
    }
    
    /**
     * 批量设置多个范围的相同样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param ranges 范围列表，如["sheetId!A1:C3", "sheetId!E1:F3"]
     * @param styleProperties 样式属性
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetCellStyleSameStyle(String spreadsheetToken, List<String> ranges, 
                                                         Map<String, Object> styleProperties) {
        List<Map<String, Object>> batchData = new ArrayList<>();
        batchData.add(createBatchStyleItem(ranges, styleProperties));
        return batchSetCellStyle(spreadsheetToken, batchData);
    }
    
    /**
     * 批量设置字体样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param ranges 范围列表，如["sheetId!A1:C3", "sheetId!E1:F3"]
     * @param bold 是否加粗
     * @param italic 是否斜体
     * @param fontSize 字体大小，如"12pt/1.5"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetFontStyle(String spreadsheetToken, List<String> ranges, 
                                                Boolean bold, Boolean italic, String fontSize) {
        Map<String, Object> style = new HashMap<>();
        
        Map<String, Object> font = new HashMap<>();
        if (bold != null) font.put("bold", bold);
        if (italic != null) font.put("italic", italic);
        if (fontSize != null) font.put("fontSize", fontSize);
        font.put("clean", false);
        
        style.put("font", font);
        style.put("clean", false);
        
        return batchSetCellStyleSameStyle(spreadsheetToken, ranges, style);
    }
    
    /**
     * 批量设置背景色
     * 
     * @param spreadsheetToken 电子表格token
     * @param ranges 范围列表，如["sheetId!A1:C3", "sheetId!E1:F3"]
     * @param backColor 背景色，如"#ff0000"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetBackgroundColor(String spreadsheetToken, List<String> ranges, String backColor) {
        Map<String, Object> style = new HashMap<>();
        style.put("backColor", backColor);
        style.put("clean", false);
        
        return batchSetCellStyleSameStyle(spreadsheetToken, ranges, style);
    }
    
    /**
     * 批量设置边框样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param ranges 范围列表，如["sheetId!A1:C3", "sheetId!E1:F3"]
     * @param borderType 边框类型，如"FULL_BORDER"
     * @param borderColor 边框颜色，如"#000000"
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetBorder(String spreadsheetToken, List<String> ranges, 
                                             String borderType, String borderColor) {
        Map<String, Object> style = new HashMap<>();
        style.put("borderType", borderType);
        style.put("borderColor", borderColor);
        style.put("clean", false);
        
        return batchSetCellStyleSameStyle(spreadsheetToken, ranges, style);
    }
    
    /**
     * 批量设置多种不同样式
     * 
     * @param spreadsheetToken 电子表格token
     * @param styleItems 样式项列表，每个项包含ranges和style
     * @return 设置结果，如果失败返回null
     */
    public Map<String, Object> batchSetMultipleStyles(String spreadsheetToken, 
                                                     List<Map<String, Object>> styleItems) {
        List<Map<String, Object>> batchData = new ArrayList<>();
        
        for (Map<String, Object> item : styleItems) {
            @SuppressWarnings("unchecked")
            List<String> ranges = (List<String>) item.get("ranges");
            @SuppressWarnings("unchecked")
            Map<String, Object> style = (Map<String, Object>) item.get("style");
            
            if (ranges != null && style != null) {
                batchData.add(createBatchStyleItem(ranges, style));
            }
        }
        
        return batchSetCellStyle(spreadsheetToken, batchData);
    }
    
    /**
     * 创建样式构建器
     * 辅助方法，用于创建复杂的样式配置
     */
    public static class StyleBuilder {
        private Map<String, Object> style = new HashMap<>();
        private Map<String, Object> font = new HashMap<>();
        
        public StyleBuilder font(Boolean bold, Boolean italic, String fontSize) {
            if (bold != null) font.put("bold", bold);
            if (italic != null) font.put("italic", italic);
            if (fontSize != null) font.put("fontSize", fontSize);
            font.put("clean", false);
            style.put("font", font);
            return this;
        }
        
        public StyleBuilder foreColor(String color) {
            style.put("foreColor", color);
            return this;
        }
        
        public StyleBuilder backColor(String color) {
            style.put("backColor", color);
            return this;
        }
        
        public StyleBuilder align(Integer hAlign, Integer vAlign) {
            if (hAlign != null) style.put("hAlign", hAlign);
            if (vAlign != null) style.put("vAlign", vAlign);
            return this;
        }
        
        public StyleBuilder border(String borderType, String borderColor) {
            style.put("borderType", borderType);
            style.put("borderColor", borderColor);
            return this;
        }
        
        public Map<String, Object> build() {
            style.put("clean", false);
            return style;
        }
    }
    
    // ==================== 数据插入相关方法（基于v2 HTTP） ====================
    
    /**
     * 插入数据
     * 参考官方文档：https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/prepend-data
     * API地址：POST https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheetToken}/values_prepend
     * 
     * 在电子表格工作表的指定范围的起始位置上方增加若干行，并在该范围中填充数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，如"sheetId!A1:C5"
     * @param values 数据，二维数组格式
     * @return 插入结果，如果失败返回null
     */
    public Map<String, Object> prependData(String spreadsheetToken, String range, List<List<Object>> values) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("无法获取访问凭证");
                return null;
            }
            
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values_prepend", 
                                     spreadsheetToken);
            
            // 构建valueRange对象
            Map<String, Object> valueRange = new HashMap<>();
            valueRange.put("range", range);
            valueRange.put("values", values);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("valueRange", valueRange);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("插入数据成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("插入数据失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("插入数据异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 插入单行数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，如"sheetId!A1:C1"
     * @param rowData 单行数据
     * @return 插入结果，如果失败返回null
     */
    public Map<String, Object> prependSingleRow(String spreadsheetToken, String range, List<Object> rowData) {
        List<List<Object>> values = new ArrayList<>();
        values.add(rowData);
        return prependData(spreadsheetToken, range, values);
    }
    
    /**
     * 在工作表起始位置插入多行数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startColumn 起始列（从A开始），如"A"
     * @param data 数据，二维数组格式
     * @return 插入结果，如果失败返回null
     */
    public Map<String, Object> prependDataToSheet(String spreadsheetToken, String sheetId, 
                                                 String startColumn, List<List<Object>> data) {
        if (data.isEmpty()) {
            logger.warn("插入数据为空");
            return null;
        }
        
        int rows = data.size();
        int cols = data.get(0).size(); // 获取第一行的列数
        
        // 计算结束列
        char endColumn = (char) (startColumn.charAt(0) + cols - 1);
        
        String range = String.format("%s!%s1:%c%d", sheetId, startColumn, endColumn, rows);
        return prependData(spreadsheetToken, range, data);
    }
    
    /**
     * 插入表格标题行
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param headers 标题列表
     * @return 插入结果，如果失败返回null
     */
    public Map<String, Object> prependHeaders(String spreadsheetToken, String sheetId, List<String> headers) {
        List<Object> headerRow = new ArrayList<>(headers);
        return prependSingleRow(spreadsheetToken, sheetId + "!A1", headerRow);
    }
    
    /**
     * 插入混合类型数据
     * 支持字符串、数字、URL、邮箱等多种数据类型
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，如"sheetId!A1:D5"
     * @param mixedData 混合类型数据
     * @return 插入结果，如果失败返回null
     */
    public Map<String, Object> prependMixedData(String spreadsheetToken, String range, Object[][] mixedData) {
        List<List<Object>> values = new ArrayList<>();
        
        for (Object[] row : mixedData) {
            List<Object> rowList = Arrays.asList(row);
            values.add(rowList);
        }
        
        return prependData(spreadsheetToken, range, values);
    }
    
    /**
     * 创建数据构建器
     * 辅助方法，用于构建复杂的数据结构
     */
    public static class DataBuilder {
        private List<List<Object>> data = new ArrayList<>();
        
        public DataBuilder addRow(Object... cells) {
            List<Object> row = Arrays.asList(cells);
            data.add(row);
            return this;
        }
        
        public DataBuilder addRow(List<Object> row) {
            data.add(new ArrayList<>(row));
            return this;
        }
        
        public DataBuilder addStringRow(String... strings) {
            List<Object> row = Arrays.asList(strings);
            data.add(row);
            return this;
        }
        
        public DataBuilder addNumberRow(Number... numbers) {
            List<Object> row = Arrays.asList(numbers);
            data.add(row);
            return this;
        }
        
        public DataBuilder addMixedRow(Object... objects) {
            List<Object> row = Arrays.asList(objects);
            data.add(row);
            return this;
        }
        
        public DataBuilder clear() {
            data.clear();
            return this;
        }
        
        public List<List<Object>> build() {
            return new ArrayList<>(data);
        }
        
        public int getRowCount() {
            return data.size();
        }
        
        public int getColumnCount() {
            return data.isEmpty() ? 0 : data.get(0).size();
        }
    }
    

    
    /**
     * 在电子表格工作表的指定范围中追加数据
     * 会在空白位置中追加数据，例如A1、A2、A3等位置找到第一个空白位置写入
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，格式如 "sheetId!A1:B4"
     * @param values 要追加的数据，二维数组
     * @param insertDataOption 追加数据方式：OVERWRITE（覆盖，默认）或 INSERT_ROWS（插入行）
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendData(String spreadsheetToken, String range, 
                                         List<List<Object>> values, String insertDataOption) {
        try {
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/values_append";
            if (insertDataOption != null && !insertDataOption.isEmpty()) {
                url += "?insertDataOption=" + insertDataOption;
            }
            
            String accessToken = getTenantAccessToken();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            Map<String, Object> valueRange = new HashMap<>();
            valueRange.put("range", range);
            valueRange.put("values", values);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("valueRange", valueRange);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("追加数据成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("追加数据失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("追加数据异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 在电子表格工作表的指定范围中追加数据（默认覆盖模式）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，格式如 "sheetId!A1:B4"
     * @param values 要追加的数据，二维数组
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendData(String spreadsheetToken, String range, List<List<Object>> values) {
        return appendData(spreadsheetToken, range, values, "OVERWRITE");
    }
    
    /**
     * 追加单行数据到指定范围
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围，格式如 "sheetId!A1:B1"
     * @param rowData 单行数据
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendSingleRow(String spreadsheetToken, String range, List<Object> rowData) {
        List<List<Object>> values = new ArrayList<>();
        values.add(rowData);
        return appendData(spreadsheetToken, range, values);
    }
    
    /**
     * 追加数据到工作表（自动构建范围）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startColumn 起始列，如 "A"
     * @param data 要追加的数据
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendDataToSheet(String spreadsheetToken, String sheetId, 
                                                String startColumn, List<List<Object>> data) {
        if (data == null || data.isEmpty()) {
            logger.warn("追加数据为空");
            return null;
        }
        
        int rowCount = data.size();
        int colCount = data.get(0).size();
        
        // 构建范围，如 "sheetId!A1:C10"
        String endColumn = String.valueOf((char) ('A' + colCount - 1));
        String range = sheetId + "!" + startColumn + "1:" + endColumn + rowCount;
        
        return appendData(spreadsheetToken, range, data);
    }
    
    /**
     * 追加表头到工作表
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param headers 表头列表
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendHeaders(String spreadsheetToken, String sheetId, List<String> headers) {
        List<Object> headerRow = new ArrayList<>(headers);
        String range = sheetId + "!A1:" + String.valueOf((char) ('A' + headers.size() - 1)) + "1";
        return appendSingleRow(spreadsheetToken, range, headerRow);
    }
    
    /**
     * 追加混合数据（支持二维数组）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围
     * @param mixedData 混合数据，二维数组
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendMixedData(String spreadsheetToken, String range, Object[][] mixedData) {
        List<List<Object>> values = new ArrayList<>();
        for (Object[] row : mixedData) {
            values.add(Arrays.asList(row));
        }
        return appendData(spreadsheetToken, range, values);
    }
    
    /**
     * 使用插入行模式追加数据（不会覆盖现有数据）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 范围
     * @param values 要追加的数据
     * @return 追加结果，如果失败返回null
     */
    public Map<String, Object> appendDataWithInsertRows(String spreadsheetToken, String range, 
                                                       List<List<Object>> values) {
        return appendData(spreadsheetToken, range, values, "INSERT_ROWS");
    }
    
    /**
     * 读取电子表格中多个指定范围的数据
     * API: GET /sheets/v2/spreadsheets/{token}/values_batch_get
     * 官方文档: https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/reading-multiple-ranges
     * 
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔，如 "Q7PlXT!A2:B6,0b6377!B1:C8"
     * @return 多个范围的数据结果
     */
    public Map<String, Object> readMultipleRanges(String spreadsheetToken, String ranges) {
        return readMultipleRanges(spreadsheetToken, ranges, null, null, null);
    }

    /**
     * 读取电子表格中多个指定范围的数据（完整参数版本）
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔，如 "Q7PlXT!A2:B6,0b6377!B1:C8"
     * @param valueRenderOption 数据格式 (ToString/Formula/FormattedValue/UnformattedValue)
     * @param dateTimeRenderOption 日期时间格式 (FormattedString)
     * @param userIdType 用户ID类型 (open_id/union_id/lark_id)
     * @return 多个范围的数据结果
     */
    public Map<String, Object> readMultipleRanges(String spreadsheetToken, String ranges, String valueRenderOption, 
                                          String dateTimeRenderOption, String userIdType) {
        try {
            // 构建URL
            String baseUrl = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values_batch_get", 
                                         spreadsheetToken);
            
            // 构建查询参数
            StringBuilder urlBuilder = new StringBuilder(baseUrl);
            urlBuilder.append("?ranges=").append(ranges);
            
            if (valueRenderOption != null && !valueRenderOption.trim().isEmpty()) {
                urlBuilder.append("&valueRenderOption=").append(valueRenderOption);
            }
            if (dateTimeRenderOption != null && !dateTimeRenderOption.trim().isEmpty()) {
                urlBuilder.append("&dateTimeRenderOption=").append(dateTimeRenderOption);
            }
            if (userIdType != null && !userIdType.trim().isEmpty()) {
                urlBuilder.append("&user_id_type=").append(userIdType);
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + getTenantAccessToken());
            headers.set("Content-Type", "application/json; charset=utf-8");
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                urlBuilder.toString(), HttpMethod.GET, entity, String.class);
            
            // 解析响应
            Map<String, Object> result = objectMapper.readValue(response.getBody(), 
                new TypeReference<Map<String, Object>>() {});
            
            logger.info("读取多个范围数据成功: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("读取多个范围数据失败: ", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "读取多个范围数据失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 读取多个范围并提取数据值
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔
     * @param valueRenderOption 数据格式
     * @return 解析后的值范围数组
     */
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> readMultipleRangesExtractValues(String spreadsheetToken, String ranges, 
                                                                           String valueRenderOption) {
        try {
            Map<String, Object> result = readMultipleRanges(spreadsheetToken, ranges, valueRenderOption, null, null);
            List<Map<String, Object>> valueRanges = new ArrayList<>();
            
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null && data.containsKey("valueRanges")) {
                List<Map<String, Object>> rawValueRanges = (List<Map<String, Object>>) data.get("valueRanges");
                for (Map<String, Object> rawRange : rawValueRanges) {
                    Map<String, Object> rangeData = new HashMap<>();
                    rangeData.put("range", rawRange.getOrDefault("range", ""));
                    rangeData.put("majorDimension", rawRange.getOrDefault("majorDimension", "ROWS"));
                    rangeData.put("revision", rawRange.getOrDefault("revision", 0));
                    
                    // 提取values
                    List<List<Object>> values = new ArrayList<>();
                    if (rawRange.containsKey("values")) {
                        List<List<Object>> rawValues = (List<List<Object>>) rawRange.get("values");
                        values.addAll(rawValues);
                    }
                    rangeData.put("values", values);
                    valueRanges.add(rangeData);
                }
            }
            
            return valueRanges;
            
        } catch (Exception e) {
            logger.error("提取多个范围数据值失败: ", e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量读取多个范围的数据（按格式返回）
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔
     * @return 格式化的读取结果
     */
    public Map<String, Object> readMultipleRangesFormatted(String spreadsheetToken, String ranges) {
        return readMultipleRanges(spreadsheetToken, ranges, "FormattedValue", "FormattedString", null);
    }

    /**
     * 批量读取多个范围的原始数据
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔
     * @return 原始读取结果
     */
    public Map<String, Object> readMultipleRangesUnformatted(String spreadsheetToken, String ranges) {
        return readMultipleRanges(spreadsheetToken, ranges, "UnformattedValue", null, null);
    }

    /**
     * 批量读取多个范围的文本数据
     * @param spreadsheetToken 电子表格token
     * @param ranges 多个查询范围，逗号分隔
     * @return 文本格式读取结果
     */
    public Map<String, Object> readMultipleRangesAsText(String spreadsheetToken, String ranges) {
        return readMultipleRanges(spreadsheetToken, ranges, "ToString", null, null);
    }

    /**
     * 向电子表格单个指定范围写入数据
     * API: PUT /sheets/v2/spreadsheets/{token}/values
     * 官方文档: https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/write-data-to-a-single-range
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 写入范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @param values 要写入的数据
     * @return 写入结果
     */
    public Map<String, Object> writeSingleRange(String spreadsheetToken, String range, List<List<Object>> values) {
        try {
            // 构建URL
            String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values", 
                                     spreadsheetToken);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> valueRange = new HashMap<>();
            valueRange.put("range", range);
            valueRange.put("values", values);
            requestBody.put("valueRange", valueRange);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + getTenantAccessToken());
            headers.set("Content-Type", "application/json; charset=utf-8");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.PUT, entity, String.class);
            
            // 解析响应
            Map<String, Object> result = objectMapper.readValue(response.getBody(), 
                new TypeReference<Map<String, Object>>() {});
            
            logger.info("向单个范围写入数据成功: {}", result);
            return result;
            
        } catch (Exception e) {
            logger.error("向单个范围写入数据失败: ", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "向单个范围写入数据失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 向指定范围写入单行数据
     * @param spreadsheetToken 电子表格token
     * @param range 写入范围
     * @param rowData 单行数据
     * @return 写入结果
     */
    public Map<String, Object> writeSingleRow(String spreadsheetToken, String range, List<Object> rowData) {
        List<List<Object>> values = new ArrayList<>();
        values.add(rowData);
        return writeSingleRange(spreadsheetToken, range, values);
    }

    /**
     * 向指定工作表写入数据
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startCell 起始单元格，如"A1"
     * @param data 要写入的数据
     * @return 写入结果
     */
    public Map<String, Object> writeDataToSheet(String spreadsheetToken, String sheetId, 
                                          String startCell, List<List<Object>> data) {
        // 计算结束位置
        int rows = data.size();
        int cols = data.isEmpty() ? 0 : data.get(0).size();
        
        // 解析起始位置
        String startCol = startCell.replaceAll("\\d", "");
        int startRow = Integer.parseInt(startCell.replaceAll("[A-Z]", ""));
        
        // 计算结束列
        String endCol = getColumnLetter(getColumnIndex(startCol) + cols - 1);
        int endRow = startRow + rows - 1;
        
        String range = String.format("%s!%s:%s%d", sheetId, startCell, endCol, endRow);
        return writeSingleRange(spreadsheetToken, range, data);
    }

    /**
     * 使用DataBuilder写入数据
     * @param spreadsheetToken 电子表格token
     * @param range 写入范围
     * @param dataBuilder 数据构建器
     * @return 写入结果
     */
    public Map<String, Object> writeDataWithBuilder(String spreadsheetToken, String range, DataBuilder dataBuilder) {
        return writeSingleRange(spreadsheetToken, range, dataBuilder.build());
    }

    /**
     * 覆盖写入数据（清空后写入）
     * @param spreadsheetToken 电子表格token
     * @param range 写入范围
     * @param values 要写入的数据
     * @return 写入结果
     */
    public Map<String, Object> overwriteRange(String spreadsheetToken, String range, List<List<Object>> values) {
        // 此API默认就是覆盖模式
        return writeSingleRange(spreadsheetToken, range, values);
    }

    /**
     * 写入表格头部数据
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param headers 表头数据
     * @return 写入结果
     */
    public Map<String, Object> writeHeaders(String spreadsheetToken, String sheetId, List<String> headers) {
        List<Object> headerRow = new ArrayList<>(headers);
        return writeSingleRow(spreadsheetToken, sheetId + "!A1:" + getColumnLetter(headers.size() - 1) + "1", headerRow);
    }

    /**
     * 批量写入混合数据类型
     * @param spreadsheetToken 电子表格token
     * @param range 写入范围
     * @param mixedData 混合数据数组
     * @return 写入结果
     */
    public Map<String, Object> writeMixedData(String spreadsheetToken, String range, Object[][] mixedData) {
        List<List<Object>> values = new ArrayList<>();
        for (Object[] row : mixedData) {
            List<Object> rowList = new ArrayList<>();
            for (Object cell : row) {
                rowList.add(cell);
            }
            values.add(rowList);
        }
        return writeSingleRange(spreadsheetToken, range, values);
    }

    /**
     * 向电子表格多个指定范围写入数据
     * API: POST /sheets/v2/spreadsheets/{token}/values_batch_update
     * 官方文档: https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/write-data-to-multiple-ranges
     * 
     * @param spreadsheetToken 电子表格token
     * @param valueRanges 多个范围和数据的映射，格式：[{range: "A1:C1", values: [[...]]}, ...]
     * @return 批量写入结果，包含每个范围的更新信息
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> writeMultipleRanges(String spreadsheetToken, 
                                                  List<Map<String, Object>> valueRanges) {
        String url = String.format("https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/%s/values_batch_update", 
                                 spreadsheetToken);
        
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("valueRanges", valueRanges);
        
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(getTenantAccessToken());
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, entity, Map.class);
            Map<String, Object> result = response.getBody();
            
            if (result != null && Integer.valueOf(0).equals(result.get("code"))) {
                logger.info("向多个范围写入数据成功，spreadsheetToken: {}, 范围数量: {}", 
                          spreadsheetToken, valueRanges.size());
                return result;
            } else {
                logger.error("向多个范围写入数据失败，响应: {}", result);
                return result;
            }
        } catch (Exception e) {
            logger.error("向多个范围写入数据异常，spreadsheetToken: {}, error: {}", 
                       spreadsheetToken, e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "向多个范围写入数据异常: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量写入数据到多个范围（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param rangeDataMap 范围和数据的映射，如 {"A1:C1": [[...]], "A3:B5": [[...]]}
     * @return 批量写入结果
     */
    public Map<String, Object> writeMultipleRanges(String spreadsheetToken, 
                                                  Map<String, List<List<Object>>> rangeDataMap) {
        List<Map<String, Object>> valueRanges = new ArrayList<>();
        
        for (Map.Entry<String, List<List<Object>>> entry : rangeDataMap.entrySet()) {
            Map<String, Object> valueRange = new HashMap<>();
            valueRange.put("range", entry.getKey());
            valueRange.put("values", entry.getValue());
            valueRanges.add(valueRange);
        }
        
        return writeMultipleRanges(spreadsheetToken, valueRanges);
    }

    /**
     * 批量写入数据到多个工作表的相同位置
     * @param spreadsheetToken 电子表格token
     * @param sheetNames 工作表名称列表
     * @param range 在每个工作表中的写入范围，如"A1:C3"
     * @param values 要写入的数据
     * @return 批量写入结果
     */
    public Map<String, Object> writeToMultipleSheets(String spreadsheetToken, 
                                                    List<String> sheetNames, 
                                                    String range, 
                                                    List<List<Object>> values) {
        List<Map<String, Object>> valueRanges = new ArrayList<>();
        
        for (String sheetName : sheetNames) {
            Map<String, Object> valueRange = new HashMap<>();
            valueRange.put("range", sheetName + "!" + range);
            valueRange.put("values", values);
            valueRanges.add(valueRange);
        }
        
        return writeMultipleRanges(spreadsheetToken, valueRanges);
    }

    /**
     * 批量写入不同数据到多个工作表
     * @param spreadsheetToken 电子表格token
     * @param sheetDataMap 工作表和数据的映射，如 {"Sheet1": {range: "A1:B2", values: [...]}, ...}
     * @return 批量写入结果
     */
    public Map<String, Object> writeToMultipleSheetsWithDifferentData(String spreadsheetToken, 
                                                                     Map<String, Map<String, Object>> sheetDataMap) {
        List<Map<String, Object>> valueRanges = new ArrayList<>();
        
        for (Map.Entry<String, Map<String, Object>> entry : sheetDataMap.entrySet()) {
            String sheetName = entry.getKey();
            Map<String, Object> sheetData = entry.getValue();
            
            Map<String, Object> valueRange = new HashMap<>();
            String range = (String) sheetData.get("range");
            valueRange.put("range", sheetName + "!" + range);
            valueRange.put("values", sheetData.get("values"));
            valueRanges.add(valueRange);
        }
        
        return writeMultipleRanges(spreadsheetToken, valueRanges);
    }

    /**
     * 批量写入头部数据到多个工作表
     * @param spreadsheetToken 电子表格token
     * @param sheetNames 工作表名称列表
     * @param headers 头部数据
     * @param startCell 起始单元格，如"A1"
     * @return 批量写入结果
     */
    public Map<String, Object> writeHeadersToMultipleSheets(String spreadsheetToken, 
                                                           List<String> sheetNames, 
                                                           List<Object> headers, 
                                                           String startCell) {
        List<List<Object>> headerData = Arrays.asList(headers);
        String range = startCell + ":" + getColumnLetter(headers.size() - 1) + "1";
        
        return writeToMultipleSheets(spreadsheetToken, sheetNames, range, headerData);
    }

    /**
     * 使用DataBuilder批量写入数据到多个范围
     * @param spreadsheetToken 电子表格token
     * @param rangeBuilderMap 范围和DataBuilder的映射
     * @return 批量写入结果
     */
    public Map<String, Object> writeMultipleRangesWithBuilder(String spreadsheetToken, 
                                                            Map<String, DataBuilder> rangeBuilderMap) {
        Map<String, List<List<Object>>> rangeDataMap = new HashMap<>();
        
        for (Map.Entry<String, DataBuilder> entry : rangeBuilderMap.entrySet()) {
            rangeDataMap.put(entry.getKey(), entry.getValue().build());
        }
        
        return writeMultipleRanges(spreadsheetToken, rangeDataMap);
    }

    /**
     * 创建筛选
     * API: POST /sheets/v3/spreadsheets/{spreadsheet_token}/sheets/{sheet_id}/filter
     * 官方文档: https://open.feishu.cn/document/server-docs/docs/sheets-v3/spreadsheet-sheet-filter/create
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围，如"8fe9d6!A1:H14"
     * @param col 应用筛选条件的列，如"E"
     * @param condition 筛选条件
     * @return 创建筛选结果，成功返回true，失败返回false
     */
    public boolean createSheetFilter(String spreadsheetToken, String sheetId, String range, String col, 
                                   com.lark.oapi.service.sheets.v3.model.Condition condition) {
        try {
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.CreateSpreadsheetSheetFilterReq req = 
                com.lark.oapi.service.sheets.v3.model.CreateSpreadsheetSheetFilterReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .createSheetFilter(com.lark.oapi.service.sheets.v3.model.CreateSheetFilter.newBuilder()
                        .range(range)
                        .col(col)
                        .condition(condition)
                        .build())
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.CreateSpreadsheetSheetFilterResp resp = 
                client.sheets().v3().spreadsheetSheetFilter().create(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("创建筛选失败，code: {}, msg: {}, reqId: {}", 
                           resp.getCode(), resp.getMsg(), resp.getRequestId());
                return false;
            }
            
            logger.info("创建筛选成功，spreadsheetToken: {}, sheetId: {}, range: {}, col: {}", 
                      spreadsheetToken, sheetId, range, col);
            return true;
            
        } catch (Exception e) {
            logger.error("创建筛选异常，spreadsheetToken: {}, sheetId: {}, error: {}", 
                       spreadsheetToken, sheetId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建数字筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param compareType 比较类型：equal, notEqual, greater, greaterOrEqual, less, lessOrEqual, between, notBetween
     * @param expected 期望值数组
     * @return 创建筛选是否成功
     */
    public boolean createNumberFilter(String spreadsheetToken, String sheetId, String range, String col, 
            String compareType, String[] expected) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("number")
                .compareType(compareType)
                .expected(expected)
                .build();
                
        return createSheetFilter(spreadsheetToken, sheetId, range, col, condition);
    }

    /**
     * 创建文本筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param compareType 比较类型：beginsWith, notBeginsWith, endsWith, notEndsWith, contains, notContains
     * @param expected 期望文本
     * @return 创建筛选是否成功
     */
    public boolean createTextFilter(String spreadsheetToken, String sheetId, String range, String col, 
            String compareType, String expected) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("text")
                .compareType(compareType)
                .expected(new String[]{expected})
                .build();
                
        return createSheetFilter(spreadsheetToken, sheetId, range, col, condition);
    }

    /**
     * 创建多值筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param values 要展示的值数组
     * @return 创建筛选是否成功
     */
    public boolean createMultiValueFilter(String spreadsheetToken, String sheetId, String range, String col, String[] values) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("multiValue")
                .expected(values)
                .build();
                
        return createSheetFilter(spreadsheetToken, sheetId, range, col, condition);
    }

    /**
     * 创建颜色筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param compareType 比较类型：backColor, foreColor
     * @param colorHex 颜色十六进制代码，如"#ffffff"
     * @return 创建筛选是否成功
     */
    public boolean createColorFilter(String spreadsheetToken, String sheetId, String range, String col, 
            String compareType, String colorHex) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("color")
                .compareType(compareType)
                .expected(new String[]{colorHex})
                .build();
                
        return createSheetFilter(spreadsheetToken, sheetId, range, col, condition);
    }

    /**
     * 清除筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @return 创建筛选是否成功
     */
    public boolean clearFilter(String spreadsheetToken, String sheetId, String range, String col) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("clear")
                .build();
                
        return createSheetFilter(spreadsheetToken, sheetId, range, col, condition);
    }

    /**
     * 创建小于筛选（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param value 比较值
     * @return 创建筛选是否成功
     */
    public boolean createLessThanFilter(String spreadsheetToken, String sheetId, String range, String col, String value) {
        return createNumberFilter(spreadsheetToken, sheetId, range, col, "less", new String[]{value});
    }

    /**
     * 创建大于筛选（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param value 比较值
     * @return 创建筛选是否成功
     */
    public boolean createGreaterThanFilter(String spreadsheetToken, String sheetId, String range, String col, String value) {
        return createNumberFilter(spreadsheetToken, sheetId, range, col, "greater", new String[]{value});
    }

    /**
     * 创建等于筛选（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param value 比较值
     * @return 创建筛选是否成功
     */
    public boolean createEqualFilter(String spreadsheetToken, String sheetId, String range, String col, String value) {
        return createNumberFilter(spreadsheetToken, sheetId, range, col, "equal", new String[]{value});
    }

    /**
     * 创建介于筛选（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 创建筛选是否成功
     */
    public boolean createBetweenFilter(String spreadsheetToken, String sheetId, String range, String col, String minValue, String maxValue) {
        return createNumberFilter(spreadsheetToken, sheetId, range, col, "between", new String[]{minValue, maxValue});
    }

    /**
     * 创建包含文本筛选（便捷方法）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param range 筛选范围
     * @param col 筛选列
     * @param text 包含的文本
     * @return 创建筛选是否成功
     */
    public boolean createContainsFilter(String spreadsheetToken, String sheetId, String range, String col, String text) {
        return createTextFilter(spreadsheetToken, sheetId, range, col, "contains", text);
    }

    /**
     * 辅助方法：根据列索引获取列字母
     */
    private String getColumnLetter(int columnIndex) {
        StringBuilder result = new StringBuilder();
        while (columnIndex >= 0) {
            result.insert(0, (char) ('A' + columnIndex % 26));
            columnIndex = columnIndex / 26 - 1;
        }
        return result.toString();
    }

    /**
     * 辅助方法：根据列字母获取列索引
     */
    private int getColumnIndex(String columnLetter) {
        int result = 0;
        for (int i = 0; i < columnLetter.length(); i++) {
            result = result * 26 + (columnLetter.charAt(i) - 'A' + 1);
        }
        return result - 1;
    }

    /**
     * 读取电子表格中单个指定范围的数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @param valueRenderOption 指定单元格数据的格式
     * @param dateTimeRenderOption 指定日期、时间、或时间日期的单元格数据的格式
     * @param userIdType 当单元格中包含@用户等涉及用户信息的元素时，该参数可指定返回的用户 ID 类型
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRange(String spreadsheetToken, String range, 
                                              String valueRenderOption, String dateTimeRenderOption, 
                                              String userIdType) {
        try {
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/values/" + range;
            
            // 构建查询参数
            StringBuilder queryParams = new StringBuilder();
            if (valueRenderOption != null && !valueRenderOption.isEmpty()) {
                queryParams.append("valueRenderOption=").append(valueRenderOption);
            }
            if (dateTimeRenderOption != null && !dateTimeRenderOption.isEmpty()) {
                if (queryParams.length() > 0) queryParams.append("&");
                queryParams.append("dateTimeRenderOption=").append(dateTimeRenderOption);
            }
            if (userIdType != null && !userIdType.isEmpty()) {
                if (queryParams.length() > 0) queryParams.append("&");
                queryParams.append("user_id_type=").append(userIdType);
            }
            
            if (queryParams.length() > 0) {
                url += "?" + queryParams.toString();
            }
            
            String accessToken = getTenantAccessToken();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<Void> request = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("读取单个范围数据成功: {}", body.get("data"));
                    return (Map<String, Object>) body.get("data");
                } else {
                    logger.error("读取单个范围数据失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("读取单个范围数据异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 读取电子表格中单个指定范围的数据（简化版本）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRange(String spreadsheetToken, String range) {
        return readSingleRange(spreadsheetToken, range, null, null, null);
    }
    
    /**
     * 读取电子表格中单个指定范围的数据（指定数据格式）
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @param valueRenderOption 指定单元格数据的格式：ToString、Formula、FormattedValue、UnformattedValue
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRange(String spreadsheetToken, String range, String valueRenderOption) {
        return readSingleRange(spreadsheetToken, range, valueRenderOption, null, null);
    }
    
    /**
     * 读取电子表格中单个指定范围的数据，返回纯文本格式
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRangeAsText(String spreadsheetToken, String range) {
        return readSingleRange(spreadsheetToken, range, "ToString", null, null);
    }
    
    /**
     * 读取电子表格中单个指定范围的数据，返回公式本身
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRangeAsFormula(String spreadsheetToken, String range) {
        return readSingleRange(spreadsheetToken, range, "Formula", null, null);
    }
    
    /**
     * 读取电子表格中单个指定范围的数据，计算并格式化单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRangeFormatted(String spreadsheetToken, String range) {
        return readSingleRange(spreadsheetToken, range, "FormattedValue", "FormattedString", null);
    }
    
    /**
     * 读取电子表格中单个指定范围的数据，计算但不格式化单元格
     * 
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 "<sheetId>!<开始位置>:<结束位置>"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSingleRangeUnformatted(String spreadsheetToken, String range) {
        return readSingleRange(spreadsheetToken, range, "UnformattedValue", null, null);
    }
    
    /**
     * 读取工作表中的所有数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSheetAllData(String spreadsheetToken, String sheetId) {
        String range = sheetId + "!A:Z";
        return readSingleRange(spreadsheetToken, range);
    }
    
    /**
     * 读取工作表中指定行的数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param rowIndex 行索引（从1开始）
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSheetRow(String spreadsheetToken, String sheetId, Integer rowIndex) {
        String range = sheetId + "!" + rowIndex + ":" + rowIndex;
        return readSingleRange(spreadsheetToken, range);
    }
    
    /**
     * 读取工作表中指定列的数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param columnLetter 列字母，如 "A"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSheetColumn(String spreadsheetToken, String sheetId, String columnLetter) {
        String range = sheetId + "!" + columnLetter + ":" + columnLetter;
        return readSingleRange(spreadsheetToken, range);
    }
    
    /**
     * 读取工作表中指定单元格的数据
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellAddress 单元格地址，如 "A1"
     * @return 读取结果，如果失败返回null
     */
    public Map<String, Object> readSheetCell(String spreadsheetToken, String sheetId, String cellAddress) {
        String range = sheetId + "!" + cellAddress;
        return readSingleRange(spreadsheetToken, range);
    }
    
    /**
     * 从读取结果中提取values数组
     * 
     * @param readResult 读取单个范围的结果
     * @return values数组，如果失败返回null
     */
    public List<List<Object>> extractValuesFromReadResult(Map<String, Object> readResult) {
        if (readResult == null) {
            return null;
        }
        
        try {
            Map<String, Object> valueRange = (Map<String, Object>) readResult.get("valueRange");
            if (valueRange != null) {
                return (List<List<Object>>) valueRange.get("values");
            }
        } catch (Exception e) {
            logger.error("提取values数组异常: {}", e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 从读取结果中提取单个单元格的值
     * 
     * @param readResult 读取单个范围的结果
     * @param rowIndex 行索引（从0开始）
     * @param colIndex 列索引（从0开始）
     * @return 单元格值，如果失败返回null
     */
    public Object extractCellValue(Map<String, Object> readResult, int rowIndex, int colIndex) {
        List<List<Object>> values = extractValuesFromReadResult(readResult);
        if (values != null && rowIndex < values.size()) {
            List<Object> row = values.get(rowIndex);
            if (row != null && colIndex < row.size()) {
                return row.get(colIndex);
            }
        }
        return null;
    }
    
    /**
     * 从读取结果中提取指定行的数据
     * 
     * @param readResult 读取单个范围的结果
     * @param rowIndex 行索引（从0开始）
     * @return 行数据，如果失败返回null
     */
    public List<Object> extractRowData(Map<String, Object> readResult, int rowIndex) {
        List<List<Object>> values = extractValuesFromReadResult(readResult);
        if (values != null && rowIndex < values.size()) {
            return values.get(rowIndex);
        }
        return null;
    }

    /**
     * 更新工作表筛选器
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @param condition 筛选条件
     * @return 更新筛选是否成功
     */
    public boolean updateSheetFilter(String spreadsheetToken, String sheetId, String col, 
                                   com.lark.oapi.service.sheets.v3.model.Condition condition) {
        try {
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.UpdateSpreadsheetSheetFilterReq req = 
                com.lark.oapi.service.sheets.v3.model.UpdateSpreadsheetSheetFilterReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .updateSheetFilter(com.lark.oapi.service.sheets.v3.model.UpdateSheetFilter.newBuilder()
                        .col(col)
                        .condition(condition)
                        .build())
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.UpdateSpreadsheetSheetFilterResp resp = 
                client.sheets().v3().spreadsheetSheetFilter().update(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("更新筛选失败，code: {}, msg: {}, reqId: {}", 
                           resp.getCode(), resp.getMsg(), resp.getRequestId());
                return false;
            }
            
            logger.info("更新筛选成功，spreadsheetToken: {}, sheetId: {}, col: {}", 
                      spreadsheetToken, sheetId, col);
            return true;
            
        } catch (Exception e) {
            logger.error("更新筛选异常，spreadsheetToken: {}, sheetId: {}, error: {}", 
                       spreadsheetToken, sheetId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新数字筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @param compareType 比较类型：equal, notEqual, greater, greaterOrEqual, less, lessOrEqual, between, notBetween
     * @param expected 期望值数组
     * @return 更新筛选是否成功
     */
    public boolean updateNumberFilter(String spreadsheetToken, String sheetId, String col, 
            String compareType, String[] expected) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("number")
                .compareType(compareType)
                .expected(expected)
                .build();
                
        return updateSheetFilter(spreadsheetToken, sheetId, col, condition);
    }

    /**
     * 更新文本筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @param compareType 比较类型：beginsWith, notBeginsWith, endsWith, notEndsWith, contains, notContains
     * @param expected 期望文本
     * @return 更新筛选是否成功
     */
    public boolean updateTextFilter(String spreadsheetToken, String sheetId, String col, 
            String compareType, String expected) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("text")
                .compareType(compareType)
                .expected(new String[]{expected})
                .build();
                
        return updateSheetFilter(spreadsheetToken, sheetId, col, condition);
    }

    /**
     * 更新多值筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @param values 要展示的值数组
     * @return 更新筛选是否成功
     */
    public boolean updateMultiValueFilter(String spreadsheetToken, String sheetId, String col, String[] values) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("multiValue")
                .expected(values)
                .build();
                
        return updateSheetFilter(spreadsheetToken, sheetId, col, condition);
    }

    /**
     * 更新颜色筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @param compareType 比较类型：backColor, foreColor
     * @param colorHex 颜色十六进制代码，如"#ffffff"
     * @return 更新筛选是否成功
     */
    public boolean updateColorFilter(String spreadsheetToken, String sheetId, String col, 
            String compareType, String colorHex) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("color")
                .compareType(compareType)
                .expected(new String[]{colorHex})
                .build();
                
        return updateSheetFilter(spreadsheetToken, sheetId, col, condition);
    }

    /**
     * 更新为清除筛选
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 筛选列
     * @return 更新筛选是否成功
     */
    public boolean updateToClearFilter(String spreadsheetToken, String sheetId, String col) {
        
        com.lark.oapi.service.sheets.v3.model.Condition condition = 
            com.lark.oapi.service.sheets.v3.model.Condition.newBuilder()
                .filterType("clear")
                .build();
                
        return updateSheetFilter(spreadsheetToken, sheetId, col, condition);
    }

    /**
     * 更新小于筛选（便捷方法）
     */
    public boolean updateLessThanFilter(String spreadsheetToken, String sheetId, String col, String value) {
        return updateNumberFilter(spreadsheetToken, sheetId, col, "less", new String[]{value});
    }

    /**
     * 更新大于筛选（便捷方法）
     */
    public boolean updateGreaterThanFilter(String spreadsheetToken, String sheetId, String col, String value) {
        return updateNumberFilter(spreadsheetToken, sheetId, col, "greater", new String[]{value});
    }

    /**
     * 更新等于筛选（便捷方法）
     */
    public boolean updateEqualFilter(String spreadsheetToken, String sheetId, String col, String value) {
        return updateNumberFilter(spreadsheetToken, sheetId, col, "equal", new String[]{value});
    }

    /**
     * 更新介于筛选（便捷方法）
     */
    public boolean updateBetweenFilter(String spreadsheetToken, String sheetId, String col, String minValue, String maxValue) {
        return updateNumberFilter(spreadsheetToken, sheetId, col, "between", new String[]{minValue, maxValue});
    }

    /**
     * 更新包含文本筛选（便捷方法）
     */
    public boolean updateContainsFilter(String spreadsheetToken, String sheetId, String col, String text) {
        return updateTextFilter(spreadsheetToken, sheetId, col, "contains", text);
    }

    /**
     * 更新以文本开头筛选（便捷方法）
     */
    public boolean updateBeginsWithFilter(String spreadsheetToken, String sheetId, String col, String text) {
        return updateTextFilter(spreadsheetToken, sheetId, col, "beginsWith", text);
    }

    /**
     * 更新以文本结尾筛选（便捷方法）
     */
    public boolean updateEndsWithFilter(String spreadsheetToken, String sheetId, String col, String text) {
        return updateTextFilter(spreadsheetToken, sheetId, col, "endsWith", text);
    }

    /**
     * 获取工作表筛选器信息
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 筛选器信息，如果失败返回null
     */
    public com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody getSheetFilter(String spreadsheetToken, String sheetId) {
        try {
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterReq req = 
                com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterResp resp = 
                client.sheets().v3().spreadsheetSheetFilter().get(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("获取筛选器失败，code: {}, msg: {}, reqId: {}", 
                           resp.getCode(), resp.getMsg(), resp.getRequestId());
                return null;
            }
            
            logger.info("获取筛选器成功，spreadsheetToken: {}, sheetId: {}", 
                      spreadsheetToken, sheetId);
            return resp.getData();
            
        } catch (Exception e) {
            logger.error("获取筛选器异常，spreadsheetToken: {}, sheetId: {}, error: {}", 
                       spreadsheetToken, sheetId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查工作表是否存在筛选器
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 是否存在筛选器
     */
    public boolean hasSheetFilter(String spreadsheetToken, String sheetId) {
        com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody filterData = 
            getSheetFilter(spreadsheetToken, sheetId);
        return filterData != null;
    }

    /**
     * 获取筛选器的范围信息
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 筛选器范围，如果不存在返回null
     */
    public String getSheetFilterRange(String spreadsheetToken, String sheetId) {
        com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody filterData = 
            getSheetFilter(spreadsheetToken, sheetId);
        
        if (filterData != null && filterData.getSheetFilterInfo() != null) {
            return filterData.getSheetFilterInfo().getRange();
        }
        return null;
    }

    /**
     * 获取筛选器的所有筛选条件
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 筛选条件映射（列名 -> 条件数组），如果不存在返回null
     */
    public java.util.Map<String, com.lark.oapi.service.sheets.v3.model.Condition[]> getSheetFilterConditions(String spreadsheetToken, String sheetId) {
        com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody filterData = 
            getSheetFilter(spreadsheetToken, sheetId);
        
        if (filterData != null && filterData.getSheetFilterInfo() != null && 
            filterData.getSheetFilterInfo().getFilterInfos() != null) {
            
            java.util.Map<String, com.lark.oapi.service.sheets.v3.model.Condition[]> conditionsMap = 
                new java.util.HashMap<>();
            
            for (com.lark.oapi.service.sheets.v3.model.FilterInfo filterInfo : filterData.getSheetFilterInfo().getFilterInfos()) {
                if (filterInfo.getCol() != null && filterInfo.getConditions() != null) {
                    conditionsMap.put(filterInfo.getCol(), filterInfo.getConditions());
                }
            }
            return conditionsMap;
        }
        return null;
    }

    /**
     * 获取指定列的筛选条件
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 列名
     * @return 筛选条件数组，如果不存在返回null
     */
    public com.lark.oapi.service.sheets.v3.model.Condition[] getColumnFilterCondition(String spreadsheetToken, String sheetId, String col) {
        java.util.Map<String, com.lark.oapi.service.sheets.v3.model.Condition[]> conditions = 
            getSheetFilterConditions(spreadsheetToken, sheetId);
        
        if (conditions != null) {
            return conditions.get(col);
        }
        return null;
    }

    /**
     * 检查指定列是否有筛选条件
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param col 列名
     * @return 是否有筛选条件
     */
    public boolean hasColumnFilter(String spreadsheetToken, String sheetId, String col) {
        com.lark.oapi.service.sheets.v3.model.Condition[] conditions = getColumnFilterCondition(spreadsheetToken, sheetId, col);
        return conditions != null && conditions.length > 0;
    }

    /**
     * 获取筛选器详细信息的字符串表示
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 筛选器详细信息，如果不存在返回空字符串
     */
    public String getSheetFilterInfo(String spreadsheetToken, String sheetId) {
        com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody filterData = 
            getSheetFilter(spreadsheetToken, sheetId);
        
        if (filterData == null) {
            return "无筛选器";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("筛选器信息:\n");
        
        if (filterData.getSheetFilterInfo() != null) {
            info.append("- 范围: ").append(filterData.getSheetFilterInfo().getRange()).append("\n");
            
            if (filterData.getSheetFilterInfo().getFilterInfos() != null) {
                info.append("- 筛选条件:\n");
                for (com.lark.oapi.service.sheets.v3.model.FilterInfo filterInfo : filterData.getSheetFilterInfo().getFilterInfos()) {
                    info.append("  列 ").append(filterInfo.getCol()).append(": ");
                    if (filterInfo.getConditions() != null && filterInfo.getConditions().length > 0) {
                        for (com.lark.oapi.service.sheets.v3.model.Condition condition : filterInfo.getConditions()) {
                            info.append("类型=").append(condition.getFilterType())
                                .append(", 比较=").append(condition.getCompareType());
                            if (condition.getExpected() != null) {
                                info.append(", 期望值=").append(java.util.Arrays.toString(condition.getExpected()));
                            }
                            info.append("; ");
                        }
                    }
                    info.append("\n");
                }
            }
        }
        
        return info.toString();
    }

    /**
     * 删除工作表筛选器
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 删除筛选是否成功
     */
    public boolean deleteSheetFilter(String spreadsheetToken, String sheetId) {
        try {
            Client client = getClient();
            
            // 创建请求对象
            com.lark.oapi.service.sheets.v3.model.DeleteSpreadsheetSheetFilterReq req = 
                com.lark.oapi.service.sheets.v3.model.DeleteSpreadsheetSheetFilterReq.newBuilder()
                    .spreadsheetToken(spreadsheetToken)
                    .sheetId(sheetId)
                    .build();
            
            // 发起请求
            com.lark.oapi.service.sheets.v3.model.DeleteSpreadsheetSheetFilterResp resp = 
                client.sheets().v3().spreadsheetSheetFilter().delete(req);
            
            // 处理服务端错误
            if (!resp.success()) {
                logger.error("删除筛选器失败，code: {}, msg: {}, reqId: {}", 
                           resp.getCode(), resp.getMsg(), resp.getRequestId());
                return false;
            }
            
            logger.info("删除筛选器成功，spreadsheetToken: {}, sheetId: {}", 
                      spreadsheetToken, sheetId);
            return true;
            
        } catch (Exception e) {
            logger.error("删除筛选器异常，spreadsheetToken: {}, sheetId: {}, error: {}", 
                       spreadsheetToken, sheetId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除工作表筛选器并检查结果
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 删除操作的详细结果
     */
    public String deleteSheetFilterWithResult(String spreadsheetToken, String sheetId) {
        // 首先检查是否存在筛选器
        boolean hasFilter = hasSheetFilter(spreadsheetToken, sheetId);
        if (!hasFilter) {
            return "工作表不存在筛选器，无需删除";
        }
        
        // 执行删除操作
        boolean deleteSuccess = deleteSheetFilter(spreadsheetToken, sheetId);
        if (!deleteSuccess) {
            return "删除筛选器失败";
        }
        
        // 验证删除结果
        boolean stillHasFilter = hasSheetFilter(spreadsheetToken, sheetId);
        if (stillHasFilter) {
            return "删除操作执行成功，但筛选器仍然存在";
        }
        
        return "筛选器删除成功";
    }

    /**
     * 安全删除工作表筛选器（带备份信息）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 删除结果和备份信息
     */
    public java.util.Map<String, Object> safeDeleteSheetFilter(String spreadsheetToken, String sheetId) {
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        
        try {
            // 备份筛选器信息
            com.lark.oapi.service.sheets.v3.model.GetSpreadsheetSheetFilterRespBody filterBackup = 
                getSheetFilter(spreadsheetToken, sheetId);
            
            if (filterBackup == null) {
                result.put("success", false);
                result.put("message", "工作表不存在筛选器");
                return result;
            }
            
            // 记录备份信息
            result.put("backup", filterBackup);
            result.put("backupInfo", getSheetFilterInfo(spreadsheetToken, sheetId));
            
            // 执行删除
            boolean deleteSuccess = deleteSheetFilter(spreadsheetToken, sheetId);
            result.put("success", deleteSuccess);
            result.put("message", deleteSuccess ? "筛选器删除成功" : "筛选器删除失败");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除筛选器异常: " + e.getMessage());
            logger.error("安全删除筛选器异常: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 设置下拉列表数据校验
     * @param spreadsheetToken 电子表格token
     * @param range 设置下拉选项的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @param conditionValues 下拉选项值列表
     * @return 设置是否成功
     */
    public boolean setDropdownDataValidation(String spreadsheetToken, String range, String[] conditionValues) {
        return setDropdownDataValidation(spreadsheetToken, range, conditionValues, false, false, null);
    }

    /**
     * 设置下拉列表数据校验（完整版）
     * @param spreadsheetToken 电子表格token
     * @param range 设置下拉选项的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @param conditionValues 下拉选项值列表
     * @param multipleValues 是否支持多选
     * @param highlightValidData 是否高亮有效数据
     * @param colors 下拉选项颜色列表，支持十六进制颜色代码如"#1FB6C1"
     * @return 设置是否成功
     */
    public boolean setDropdownDataValidation(String spreadsheetToken, String range, String[] conditionValues,
                                           boolean multipleValues, boolean highlightValidData, String[] colors) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dataValidation";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("range", range);
            requestBody.put("dataValidationType", "list");
            
            // 构建数据校验规则
            Map<String, Object> dataValidation = new HashMap<>();
            dataValidation.put("conditionValues", conditionValues);
            
            // 构建选项配置
            Map<String, Object> options = new HashMap<>();
            options.put("multipleValues", multipleValues);
            if (highlightValidData) {
                options.put("highlightValidData", true);
                if (colors != null && colors.length > 0) {
                    options.put("colors", colors);
                }
            }
            dataValidation.put("options", options);
            
            requestBody.put("dataValidation", dataValidation);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("设置下拉列表成功: range={}, values={}", range, java.util.Arrays.toString(conditionValues));
                    return true;
                } else {
                    logger.error("设置下拉列表失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("设置下拉列表异常: range={}, error={}", range, e.getMessage(), e);
        }
        return false;
    }

    /**
     * 设置简单下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @param options 下拉选项
     * @return 设置是否成功
     */
    public boolean setSimpleDropdown(String spreadsheetToken, String sheetId, String cellRange, String[] options) {
        String range = sheetId + "!" + cellRange;
        return setDropdownDataValidation(spreadsheetToken, range, options);
    }

    /**
     * 设置多选下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @param options 下拉选项
     * @return 设置是否成功
     */
    public boolean setMultiSelectDropdown(String spreadsheetToken, String sheetId, String cellRange, String[] options) {
        String range = sheetId + "!" + cellRange;
        return setDropdownDataValidation(spreadsheetToken, range, options, true, false, null);
    }

    /**
     * 设置彩色下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @param options 下拉选项
     * @param colors 对应的颜色列表
     * @return 设置是否成功
     */
    public boolean setColoredDropdown(String spreadsheetToken, String sheetId, String cellRange, 
                                    String[] options, String[] colors) {
        String range = sheetId + "!" + cellRange;
        return setDropdownDataValidation(spreadsheetToken, range, options, false, true, colors);
    }

    /**
     * 设置状态下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @return 设置是否成功
     */
    public boolean setStatusDropdown(String spreadsheetToken, String sheetId, String cellRange) {
        String[] statusOptions = {"待开始", "进行中", "已完成", "已暂停", "已取消"};
        String[] statusColors = {"#FFB6C1", "#1FB6C1", "#90EE90", "#FFA500", "#FF6B6B"};
        return setColoredDropdown(spreadsheetToken, sheetId, cellRange, statusOptions, statusColors);
    }

    /**
     * 设置优先级下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @return 设置是否成功
     */
    public boolean setPriorityDropdown(String spreadsheetToken, String sheetId, String cellRange) {
        String[] priorityOptions = {"低", "中", "高", "紧急"};
        String[] priorityColors = {"#90EE90", "#1FB6C1", "#FFA500", "#FF6B6B"};
        return setColoredDropdown(spreadsheetToken, sheetId, cellRange, priorityOptions, priorityColors);
    }

    /**
     * 设置是否下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @return 设置是否成功
     */
    public boolean setYesNoDropdown(String spreadsheetToken, String sheetId, String cellRange) {
        String[] yesNoOptions = {"是", "否"};
        String[] yesNoColors = {"#90EE90", "#FF6B6B"};
        return setColoredDropdown(spreadsheetToken, sheetId, cellRange, yesNoOptions, yesNoColors);
    }

    /**
     * 更新下拉列表数据校验
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param conditionValues 新的下拉选项值列表
     * @return 更新是否成功
     */
    public boolean updateDropdownDataValidation(String spreadsheetToken, String sheetId, int dataValidationId, String[] conditionValues) {
        return updateDropdownDataValidation(spreadsheetToken, sheetId, dataValidationId, conditionValues, false, false, null);
    }

    /**
     * 更新下拉列表数据校验（完整版）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param conditionValues 新的下拉选项值列表
     * @param multipleValues 是否支持多选
     * @param highlightValidData 是否高亮有效数据
     * @param colors 下拉选项颜色列表，支持十六进制颜色代码如"#1FB6C1"
     * @return 更新是否成功
     */
    public boolean updateDropdownDataValidation(String spreadsheetToken, String sheetId, int dataValidationId, String[] conditionValues,
                                              boolean multipleValues, boolean highlightValidData, String[] colors) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dataValidation/" + sheetId + "/" + dataValidationId;
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dataValidationType", "list");
            
            // 构建数据校验规则
            Map<String, Object> dataValidation = new HashMap<>();
            dataValidation.put("conditionValues", conditionValues);
            
            // 构建选项配置
            Map<String, Object> options = new HashMap<>();
            options.put("multipleValues", multipleValues);
            if (highlightValidData) {
                options.put("highlightValidData", true);
                if (colors != null && colors.length > 0) {
                    options.put("colors", colors);
                }
            }
            dataValidation.put("options", options);
            
            requestBody.put("dataValidation", dataValidation);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.PUT, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("更新下拉列表成功: sheetId={}, dataValidationId={}, values={}", 
                              sheetId, dataValidationId, java.util.Arrays.toString(conditionValues));
                    return true;
                } else {
                    logger.error("更新下拉列表失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("更新下拉列表异常: sheetId={}, dataValidationId={}, error={}", 
                       sheetId, dataValidationId, e.getMessage(), e);
        }
        return false;
    }

    /**
     * 更新下拉列表选项（简化版）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param newOptions 新的下拉选项
     * @return 更新是否成功
     */
    public boolean updateDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId, String[] newOptions) {
        return updateDropdownDataValidation(spreadsheetToken, sheetId, dataValidationId, newOptions);
    }

    /**
     * 更新多选下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param newOptions 新的下拉选项
     * @return 更新是否成功
     */
    public boolean updateMultiSelectDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId, String[] newOptions) {
        return updateDropdownDataValidation(spreadsheetToken, sheetId, dataValidationId, newOptions, true, false, null);
    }

    /**
     * 更新彩色下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param newOptions 新的下拉选项
     * @param colors 对应的颜色列表
     * @return 更新是否成功
     */
    public boolean updateColoredDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId, 
                                              String[] newOptions, String[] colors) {
        return updateDropdownDataValidation(spreadsheetToken, sheetId, dataValidationId, newOptions, false, true, colors);
    }

    /**
     * 更新状态下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @return 更新是否成功
     */
    public boolean updateStatusDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId) {
        String[] statusOptions = {"待开始", "进行中", "已完成", "已暂停", "已取消"};
        String[] statusColors = {"#FFB6C1", "#1FB6C1", "#90EE90", "#FFA500", "#FF6B6B"};
        return updateColoredDropdownOptions(spreadsheetToken, sheetId, dataValidationId, statusOptions, statusColors);
    }

    /**
     * 更新优先级下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @return 更新是否成功
     */
    public boolean updatePriorityDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId) {
        String[] priorityOptions = {"低", "中", "高", "紧急"};
        String[] priorityColors = {"#90EE90", "#1FB6C1", "#FFA500", "#FF6B6B"};
        return updateColoredDropdownOptions(spreadsheetToken, sheetId, dataValidationId, priorityOptions, priorityColors);
    }

    /**
     * 更新是否下拉列表（预定义选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @return 更新是否成功
     */
    public boolean updateYesNoDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId) {
        String[] yesNoOptions = {"是", "否"};
        String[] yesNoColors = {"#90EE90", "#FF6B6B"};
        return updateColoredDropdownOptions(spreadsheetToken, sheetId, dataValidationId, yesNoOptions, yesNoColors);
    }

    /**
     * 添加下拉列表选项（保留原有选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param additionalOptions 要添加的新选项
     * @return 更新是否成功
     */
    public boolean addDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId, String[] additionalOptions) {
        // 注意：这个方法需要先查询现有选项，然后合并新选项
        // 由于需要查询接口支持，这里提供基础实现框架
        logger.warn("添加下拉列表选项功能需要先实现查询下拉列表接口");
        return false;
    }

    /**
     * 移除下拉列表选项（保留其他选项）
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param dataValidationId 下拉列表ID
     * @param optionsToRemove 要移除的选项
     * @return 更新是否成功
     */
    public boolean removeDropdownOptions(String spreadsheetToken, String sheetId, int dataValidationId, String[] optionsToRemove) {
        // 注意：这个方法需要先查询现有选项，然后移除指定选项
        // 由于需要查询接口支持，这里提供基础实现框架
        logger.warn("移除下拉列表选项功能需要先实现查询下拉列表接口");
        return false;
    }

    /**
     * 查询下拉列表设置
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 下拉列表设置信息，如果失败返回null
     */
    public Map<String, Object> queryDropdownDataValidation(String spreadsheetToken, String range) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("获取访问令牌失败");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dataValidation"
                       + "?range=" + java.net.URLEncoder.encode(range, "UTF-8")
                       + "&dataValidationType=list";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("查询下拉列表成功: range={}", range);
                    return body;
                } else {
                    logger.error("查询下拉列表失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("查询下拉列表异常: range={}, error={}", range, e.getMessage(), e);
        }
        return null;
    }

    /**
     * 查询指定工作表范围的下拉列表设置
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @return 下拉列表设置信息，如果失败返回null
     */
    public Map<String, Object> queryDropdownInRange(String spreadsheetToken, String sheetId, String cellRange) {
        String range = sheetId + "!" + cellRange;
        return queryDropdownDataValidation(spreadsheetToken, range);
    }

    /**
     * 获取下拉列表的选项值
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 下拉选项值数组，如果失败返回null
     */
    @SuppressWarnings("unchecked")
    public String[] getDropdownOptionValues(String spreadsheetToken, String range) {
        Map<String, Object> result = queryDropdownDataValidation(spreadsheetToken, range);
        if (result != null) {
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null) {
                java.util.List<Map<String, Object>> dataValidations = 
                    (java.util.List<Map<String, Object>>) data.get("dataValidations");
                if (dataValidations != null && !dataValidations.isEmpty()) {
                    Map<String, Object> validation = dataValidations.get(0);
                    java.util.List<String> conditionValues = 
                        (java.util.List<String>) validation.get("conditionValues");
                    if (conditionValues != null) {
                        return conditionValues.toArray(new String[0]);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取下拉列表的ID
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 下拉列表ID，如果失败返回-1
     */
    @SuppressWarnings("unchecked")
    public int getDropdownDataValidationId(String spreadsheetToken, String range) {
        Map<String, Object> result = queryDropdownDataValidation(spreadsheetToken, range);
        if (result != null) {
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null) {
                java.util.List<Map<String, Object>> dataValidations = 
                    (java.util.List<Map<String, Object>>) data.get("dataValidations");
                if (dataValidations != null && !dataValidations.isEmpty()) {
                    Map<String, Object> validation = dataValidations.get(0);
                    Integer dataValidationId = (Integer) validation.get("dataValidationId");
                    if (dataValidationId != null) {
                        return dataValidationId;
                    }
                }
            }
        }
        return -1;
    }

    /**
     * 检查指定范围是否存在下拉列表
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 是否存在下拉列表
     */
    public boolean hasDropdownDataValidation(String spreadsheetToken, String range) {
        return getDropdownDataValidationId(spreadsheetToken, range) != -1;
    }

    /**
     * 获取下拉列表的详细配置信息
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 下拉列表配置信息，如果失败返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getDropdownConfiguration(String spreadsheetToken, String range) {
        Map<String, Object> result = queryDropdownDataValidation(spreadsheetToken, range);
        if (result != null) {
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null) {
                java.util.List<Map<String, Object>> dataValidations = 
                    (java.util.List<Map<String, Object>>) data.get("dataValidations");
                if (dataValidations != null && !dataValidations.isEmpty()) {
                    Map<String, Object> validation = dataValidations.get(0);
                    Map<String, Object> options = (Map<String, Object>) validation.get("options");
                    
                    Map<String, Object> config = new HashMap<>();
                    config.put("dataValidationId", validation.get("dataValidationId"));
                    config.put("conditionValues", validation.get("conditionValues"));
                    config.put("dataValidationType", validation.get("dataValidationType"));
                    
                    if (options != null) {
                        config.put("multipleValues", options.get("multipleValues"));
                        config.put("highlightValidData", options.get("highlightValidData"));
                        config.put("colorValueMap", options.get("colorValueMap"));
                    }
                    
                    return config;
                }
            }
        }
        return null;
    }

    /**
     * 检查下拉列表是否支持多选
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 是否支持多选，如果查询失败返回false
     */
    public boolean isMultiSelectDropdown(String spreadsheetToken, String range) {
        Map<String, Object> config = getDropdownConfiguration(spreadsheetToken, range);
        if (config != null) {
            Boolean multipleValues = (Boolean) config.get("multipleValues");
            return multipleValues != null && multipleValues;
        }
        return false;
    }

    /**
     * 检查下拉列表是否启用了数据高亮
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 是否启用数据高亮，如果查询失败返回false
     */
    public boolean isHighlightValidData(String spreadsheetToken, String range) {
        Map<String, Object> config = getDropdownConfiguration(spreadsheetToken, range);
        if (config != null) {
            Boolean highlightValidData = (Boolean) config.get("highlightValidData");
            return highlightValidData != null && highlightValidData;
        }
        return false;
    }

    /**
     * 获取下拉列表的颜色映射
     * @param spreadsheetToken 电子表格token
     * @param range 查询范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 颜色映射（选项值 -> 颜色代码），如果失败返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, String> getDropdownColorMapping(String spreadsheetToken, String range) {
        Map<String, Object> config = getDropdownConfiguration(spreadsheetToken, range);
        if (config != null) {
            return (Map<String, String>) config.get("colorValueMap");
        }
        return null;
    }

    /**
     * 查询工作表中所有下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 所有下拉列表的配置信息列表，如果失败返回空列表
     */
    public java.util.List<Map<String, Object>> queryAllDropdownsInSheet(String spreadsheetToken, String sheetId) {
        // 查询整个工作表的下拉列表
        String range = sheetId + "!1:5000"; // 限制在5000行内
        Map<String, Object> result = queryDropdownDataValidation(spreadsheetToken, range);
        
        java.util.List<Map<String, Object>> dropdowns = new java.util.ArrayList<>();
        if (result != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null) {
                @SuppressWarnings("unchecked")
                java.util.List<Map<String, Object>> dataValidations = 
                    (java.util.List<Map<String, Object>>) data.get("dataValidations");
                if (dataValidations != null) {
                    dropdowns.addAll(dataValidations);
                }
            }
        }
        return dropdowns;
    }

    /**
     * 删除下拉列表设置
     * @param spreadsheetToken 电子表格token
     * @param range 要删除的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @param dataValidationIds 要删除的下拉列表ID数组
     * @return 删除是否成功
     */
    public boolean deleteDropdownDataValidation(String spreadsheetToken, String range, int[] dataValidationIds) {
        java.util.List<Map<String, Object>> ranges = new java.util.ArrayList<>();
        Map<String, Object> rangeItem = new HashMap<>();
        rangeItem.put("range", range);
        rangeItem.put("dataValidationIds", dataValidationIds);
        ranges.add(rangeItem);
        
        return deleteMultipleDropdownDataValidations(spreadsheetToken, ranges);
    }

    /**
     * 批量删除多个范围的下拉列表设置
     * @param spreadsheetToken 电子表格token
     * @param dataValidationRanges 要删除的范围和ID列表
     * @return 删除是否成功
     */
    public boolean deleteMultipleDropdownDataValidations(String spreadsheetToken, 
                                                       java.util.List<Map<String, Object>> dataValidationRanges) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dataValidation";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dataValidationRanges", dataValidationRanges);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.DELETE, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("删除下拉列表成功: ranges={}", dataValidationRanges.size());
                    return true;
                } else {
                    logger.error("删除下拉列表失败: code={}, msg={}", code, body.get("msg"));
                }
            }
        } catch (Exception e) {
            logger.error("删除下拉列表异常: ranges={}, error={}", 
                       dataValidationRanges.size(), e.getMessage(), e);
        }
        return false;
    }

    /**
     * 删除指定范围的单个下拉列表
     * @param spreadsheetToken 电子表格token
     * @param range 要删除的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @param dataValidationId 要删除的下拉列表ID
     * @return 删除是否成功
     */
    public boolean deleteSingleDropdownDataValidation(String spreadsheetToken, String range, int dataValidationId) {
        return deleteDropdownDataValidation(spreadsheetToken, range, new int[]{dataValidationId});
    }

    /**
     * 删除工作表范围内的下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param cellRange 单元格范围，如"A2:A100"
     * @param dataValidationIds 要删除的下拉列表ID数组
     * @return 删除是否成功
     */
    public boolean deleteDropdownInRange(String spreadsheetToken, String sheetId, String cellRange, int[] dataValidationIds) {
        String range = sheetId + "!" + cellRange;
        return deleteDropdownDataValidation(spreadsheetToken, range, dataValidationIds);
    }

    /**
     * 删除指定范围内的所有下拉列表
     * @param spreadsheetToken 电子表格token
     * @param range 要删除的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @return 删除是否成功
     */
    public boolean deleteAllDropdownsInRange(String spreadsheetToken, String range) {
        // 先查询该范围内的所有下拉列表
        Map<String, Object> result = queryDropdownDataValidation(spreadsheetToken, range);
        if (result != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data != null) {
                @SuppressWarnings("unchecked")
                java.util.List<Map<String, Object>> dataValidations = 
                    (java.util.List<Map<String, Object>>) data.get("dataValidations");
                if (dataValidations != null && !dataValidations.isEmpty()) {
                    java.util.List<Integer> ids = new java.util.ArrayList<>();
                    for (Map<String, Object> validation : dataValidations) {
                        Integer id = (Integer) validation.get("dataValidationId");
                        if (id != null) {
                            ids.add(id);
                        }
                    }
                    if (!ids.isEmpty()) {
                        int[] idArray = ids.stream().mapToInt(Integer::intValue).toArray();
                        return deleteDropdownDataValidation(spreadsheetToken, range, idArray);
                    }
                }
            }
        }
        return false;
    }

    /**
     * 删除工作表中所有下拉列表
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @return 删除是否成功
     */
    public boolean deleteAllDropdownsInSheet(String spreadsheetToken, String sheetId) {
        String range = sheetId + "!1:5000"; // 限制在5000行内
        return deleteAllDropdownsInRange(spreadsheetToken, range);
    }

    /**
     * 获取删除下拉列表的详细结果
     * @param spreadsheetToken 电子表格token
     * @param dataValidationRanges 要删除的范围和ID列表
     * @return 删除结果详情，如果失败返回null
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> deleteDropdownDataValidationWithResult(String spreadsheetToken, 
                                                                     java.util.List<Map<String, Object>> dataValidationRanges) {
        try {
            String accessToken = getTenantAccessToken();
            if (accessToken == null) {
                logger.error("获取访问令牌失败");
                return null;
            }
            
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/dataValidation";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dataValidationRanges", dataValidationRanges);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + accessToken);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.DELETE, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Integer code = (Integer) body.get("code");
                if (code != null && code == 0) {
                    logger.info("删除下拉列表成功: ranges={}", dataValidationRanges.size());
                    return body;
                } else {
                    logger.error("删除下拉列表失败: code={}, msg={}", code, body.get("msg"));
                    return body;
                }
            }
        } catch (Exception e) {
            logger.error("删除下拉列表异常: ranges={}, error={}", 
                       dataValidationRanges.size(), e.getMessage(), e);
        }
        return null;
    }

    /**
     * 检查删除结果中每个范围的执行状态
     * @param deleteResult 删除操作的返回结果
     * @return 每个范围的执行状态列表
     */
    @SuppressWarnings("unchecked")
    public java.util.List<Map<String, Object>> getDeleteRangeResults(Map<String, Object> deleteResult) {
        if (deleteResult != null) {
            Map<String, Object> data = (Map<String, Object>) deleteResult.get("data");
            if (data != null) {
                return (java.util.List<Map<String, Object>>) data.get("rangeResults");
            }
        }
        return new java.util.ArrayList<>();
    }

    /**
     * 安全删除下拉列表（先备份再删除）
     * @param spreadsheetToken 电子表格token
     * @param range 要删除的范围，格式为 <sheetId>!<开始位置>:<结束位置>
     * @param dataValidationIds 要删除的下拉列表ID数组
     * @return 删除结果和备份信息
     */
    public Map<String, Object> safeDeleteDropdownDataValidation(String spreadsheetToken, String range, int[] dataValidationIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 先备份现有配置
            Map<String, Object> backupConfig = getDropdownConfiguration(spreadsheetToken, range);
            result.put("backup", backupConfig);
            
            // 执行删除
            boolean success = deleteDropdownDataValidation(spreadsheetToken, range, dataValidationIds);
            result.put("success", success);
            
            if (success) {
                result.put("message", "下拉列表删除成功，配置已备份");
                logger.info("安全删除下拉列表成功: range={}", range);
            } else {
                result.put("message", "下拉列表删除失败");
                logger.warn("安全删除下拉列表失败: range={}", range);
            }
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除下拉列表异常: " + e.getMessage());
            logger.error("安全删除下拉列表异常: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 批量创建条件格式
     * 参考官方API：https://open.feishu.cn/document/server-docs/docs/sheets-v2/conditionformat/batch_create
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param conditionFormats 条件格式列表，每个元素包含range、rule、style等信息
     * @return 创建结果
     */
    public Map<String, Object> batchCreateConditionFormat(String spreadsheetToken, String sheetId, List<Map<String, Object>> conditionFormats) {
        try {
            String accessToken = getTenantAccessToken();
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/sheets/" + sheetId + "/condition_formats/batch_create";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("condition_formats", conditionFormats);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && Integer.valueOf(0).equals(responseBody.get("code"))) {
                logger.info("成功批量创建条件格式，电子表格: {}, 工作表: {}, 数量: {}", spreadsheetToken, sheetId, conditionFormats.size());
                return responseBody;
            } else {
                logger.error("批量创建条件格式失败，响应: {}", responseBody);
                return responseBody != null ? responseBody : Collections.emptyMap();
            }
        } catch (Exception e) {
            logger.error("批量创建条件格式时发生错误，电子表格: {}, 工作表: {}", spreadsheetToken, sheetId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "批量创建条件格式失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量更新条件格式
     * 参考官方API：https://open.feishu.cn/document/server-docs/docs/sheets-v3/conditionformat/condition-format-update
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetConditionFormats 要更新的条件格式信息列表，每个元素包含sheet_id和condition_format
     * @return 更新结果
     */
    public Map<String, Object> batchUpdateConditionFormat(String spreadsheetToken, List<Map<String, Object>> sheetConditionFormats) {
        try {
            String accessToken = getTenantAccessToken();
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/condition_formats/batch_update";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sheet_condition_formats", sheetConditionFormats);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && Integer.valueOf(0).equals(responseBody.get("code"))) {
                logger.info("成功批量更新条件格式，电子表格: {}, 数量: {}", spreadsheetToken, sheetConditionFormats.size());
                return responseBody;
            } else {
                logger.error("批量更新条件格式失败，响应: {}", responseBody);
                return responseBody != null ? responseBody : Collections.emptyMap();
            }
        } catch (Exception e) {
            logger.error("批量更新条件格式时发生错误，电子表格: {}", spreadsheetToken, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "批量更新条件格式失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量获取条件格式
     * 参考官方API：https://open.feishu.cn/document/server-docs/docs/sheets-v3/conditionformat/condition-format-get
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetIds 工作表ID列表，多个ID使用逗号分隔，最多支持10个工作表
     * @return 获取结果，包含各工作表的条件格式信息
     */
    public Map<String, Object> batchGetConditionFormat(String spreadsheetToken, String sheetIds) {
        try {
            String accessToken = getTenantAccessToken();
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/condition_formats?sheet_ids=" + sheetIds;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && Integer.valueOf(0).equals(responseBody.get("code"))) {
                logger.info("成功批量获取条件格式，电子表格: {}, 工作表: {}", spreadsheetToken, sheetIds);
                return responseBody;
            } else {
                logger.error("批量获取条件格式失败，响应: {}", responseBody);
                return responseBody != null ? responseBody : Collections.emptyMap();
            }
        } catch (Exception e) {
            logger.error("批量获取条件格式时发生错误，电子表格: {}, 工作表: {}", spreadsheetToken, sheetIds, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "批量获取条件格式失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 批量删除条件格式
     * 参考官方API：https://open.feishu.cn/document/server-docs/docs/sheets-v3/conditionformat/condition-format-delete
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetCfIds 要删除的条件格式ID列表，每个元素包含sheet_id和cf_id，最多支持删除10个条件格式
     * @return 删除结果
     */
    public Map<String, Object> batchDeleteConditionFormat(String spreadsheetToken, List<Map<String, Object>> sheetCfIds) {
        try {
            String accessToken = getTenantAccessToken();
            String url = "https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/" + spreadsheetToken + "/condition_formats/batch_delete";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("sheet_cf_ids", sheetCfIds);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, Map.class);
            
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && Integer.valueOf(0).equals(responseBody.get("code"))) {
                logger.info("成功批量删除条件格式，电子表格: {}, 数量: {}", spreadsheetToken, sheetCfIds.size());
                return responseBody;
            } else {
                logger.error("批量删除条件格式失败，响应: {}", responseBody);
                return responseBody != null ? responseBody : Collections.emptyMap();
            }
        } catch (Exception e) {
            logger.error("批量删除条件格式时发生错误，电子表格: {}", spreadsheetToken, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", -1);
            errorResult.put("msg", "批量删除条件格式失败: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * 分批写入大数据到指定范围（解决飞书API 100列限制）
     * 飞书API限制：单次写入不得超过5000行、100列
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startCell 起始单元格，如"A1"
     * @param data 要写入的数据
     * @param maxColumns 每批最大列数，默认100
     * @return 分批写入结果
     */
    public Map<String, Object> writeLargeDataInBatches(String spreadsheetToken, String sheetId, 
                                                      String startCell, List<List<Object>> data, 
                                                      int maxColumns) {
        if (data == null || data.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 0);
            result.put("msg", "数据为空，无需写入");
            return result;
        }
        
        int totalRows = data.size();
        int totalColumns = data.get(0).size();
        
        logger.info("开始分批写入大数据，总行数: {}, 总列数: {}, 每批最大列数: {}", 
                   totalRows, totalColumns, maxColumns);
        
        // 如果列数不超过限制，直接写入
        if (totalColumns <= maxColumns) {
            logger.info("列数未超过限制，直接写入");
            return writeDataToSheet(spreadsheetToken, sheetId, startCell, data);
        }
        
        // 分批写入
        List<Map<String, Object>> batchResults = new ArrayList<>();
        int batchCount = (totalColumns + maxColumns - 1) / maxColumns; // 向上取整
        
        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            int startColumn = batchIndex * maxColumns;
            int endColumn = Math.min(startColumn + maxColumns - 1, totalColumns - 1);
            
            logger.info("写入第 {} 批，列范围: {} - {}", batchIndex + 1, startColumn, endColumn);
            
            // 提取当前批次的数据
            List<List<Object>> batchData = new ArrayList<>();
            for (List<Object> row : data) {
                List<Object> batchRow = new ArrayList<>();
                for (int col = startColumn; col <= endColumn; col++) {
                    if (col < row.size()) {
                        batchRow.add(row.get(col));
                    } else {
                        batchRow.add(""); // 填充空值
                    }
                }
                batchData.add(batchRow);
            }
            
            // 计算写入范围
            String startCol = getColumnLetter(startColumn);
            String endCol = getColumnLetter(endColumn);
            
            // 解析起始行号
            int startRow = Integer.parseInt(startCell.replaceAll("[A-Z]", ""));
            int endRow = startRow + totalRows - 1;
            
            String range = String.format("%s!%s%d:%s%d", sheetId, startCol, startRow, endCol, endRow);
            
            logger.info("写入范围: {}", range);
            
            // 写入当前批次
            Map<String, Object> batchResult = writeSingleRange(spreadsheetToken, range, batchData);
            batchResults.add(batchResult);
            
            // 检查批次结果
            Integer code = (Integer) batchResult.get("code");
            if (code == null || code != 0) {
                logger.error("第 {} 批写入失败: {}", batchIndex + 1, batchResult);
                // 返回失败结果
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("code", -1);
                errorResult.put("msg", String.format("第 %d 批写入失败: %s", batchIndex + 1, batchResult.get("msg")));
                errorResult.put("failedBatch", batchIndex + 1);
                errorResult.put("batchResults", batchResults);
                return errorResult;
            }
            
            logger.info("第 {} 批写入成功", batchIndex + 1);
        }
        
        // 所有批次都成功
        Map<String, Object> result = new HashMap<>();
        result.put("code", 0);
        result.put("msg", String.format("分批写入成功，共 %d 批", batchCount));
        result.put("totalBatches", batchCount);
        result.put("totalRows", totalRows);
        result.put("totalColumns", totalColumns);
        result.put("batchResults", batchResults);
        
        logger.info("分批写入完成，总批次: {}", batchCount);
        return result;
    }

    /**
     * 分批写入大数据到指定范围（使用默认100列限制）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startCell 起始单元格，如"A1"
     * @param data 要写入的数据
     * @return 分批写入结果
     */
    public Map<String, Object> writeLargeDataInBatches(String spreadsheetToken, String sheetId, 
                                                      String startCell, List<List<Object>> data) {
        return writeLargeDataInBatches(spreadsheetToken, sheetId, startCell, data, 100);
    }

    /**
     * 分批读取大数据（解决飞书API 100列限制）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startCell 起始单元格，如"A1"
     * @param totalRows 总行数
     * @param totalColumns 总列数
     * @param maxColumns 每批最大列数，默认100
     * @return 分批读取结果
     */
    public List<List<Object>> readLargeDataInBatches(String spreadsheetToken, String sheetId, 
                                                    String startCell, int totalRows, int totalColumns, 
                                                    int maxColumns) {
        if (totalColumns <= maxColumns) {
            // 不需要分批，直接读取
            int startRow = Integer.parseInt(startCell.replaceAll("[A-Z]", ""));
            int endRow = startRow + totalRows - 1;
            String endCol = getColumnLetter(totalColumns - 1);
            String range = String.format("%s!%s:%s%d", sheetId, startCell, endCol, endRow);
            
            Map<String, Object> result = readSingleRange(spreadsheetToken, range, "UnformattedValue", null, null);
            if (result != null && result.containsKey("values")) {
                return (List<List<Object>>) result.get("values");
            }
            return new ArrayList<>();
        }
        
        // 分批读取
        List<List<Object>> allData = new ArrayList<>();
        int batchCount = (totalColumns + maxColumns - 1) / maxColumns;
        
        logger.info("开始分批读取大数据，总行数: {}, 总列数: {}, 每批最大列数: {}, 总批次: {}", 
                   totalRows, totalColumns, maxColumns, batchCount);
        
        // 初始化结果数组
        for (int i = 0; i < totalRows; i++) {
            List<Object> row = new ArrayList<>();
            for (int j = 0; j < totalColumns; j++) {
                row.add("");
            }
            allData.add(row);
        }
        
        for (int batchIndex = 0; batchIndex < batchCount; batchIndex++) {
            int startColumn = batchIndex * maxColumns;
            int endColumn = Math.min(startColumn + maxColumns - 1, totalColumns - 1);
            
            logger.info("读取第 {} 批，列范围: {} - {}", batchIndex + 1, startColumn, endColumn);
            
            // 计算读取范围
            String startCol = getColumnLetter(startColumn);
            String endCol = getColumnLetter(endColumn);
            
            int startRow = Integer.parseInt(startCell.replaceAll("[A-Z]", ""));
            int endRow = startRow + totalRows - 1;
            
            String range = String.format("%s!%s%d:%s%d", sheetId, startCol, startRow, endCol, endRow);
            
            logger.info("读取范围: {}", range);
            
            // 读取当前批次
            Map<String, Object> batchResult = readSingleRange(spreadsheetToken, range, "UnformattedValue", null, null);
            
            if (batchResult != null && batchResult.containsKey("values")) {
                List<List<Object>> batchData = (List<List<Object>>) batchResult.get("values");
                
                // 将批次数据合并到总数据中
                for (int rowIndex = 0; rowIndex < batchData.size() && rowIndex < totalRows; rowIndex++) {
                    List<Object> batchRow = batchData.get(rowIndex);
                    List<Object> targetRow = allData.get(rowIndex);
                    
                    for (int colIndex = 0; colIndex < batchRow.size(); colIndex++) {
                        int targetColIndex = startColumn + colIndex;
                        if (targetColIndex < totalColumns) {
                            targetRow.set(targetColIndex, batchRow.get(colIndex));
                        }
                    }
                }
                
                logger.info("第 {} 批读取成功，数据行数: {}", batchIndex + 1, batchData.size());
            } else {
                logger.warn("第 {} 批读取失败或无数据", batchIndex + 1);
            }
        }
        
        logger.info("分批读取完成，总批次: {}, 最终数据行数: {}", batchCount, allData.size());
        return allData;
    }

    /**
     * 分批读取大数据（使用默认100列限制）
     * 
     * @param spreadsheetToken 电子表格token
     * @param sheetId 工作表ID
     * @param startCell 起始单元格，如"A1"
     * @param totalRows 总行数
     * @param totalColumns 总列数
     * @return 分批读取结果
     */
    public List<List<Object>> readLargeDataInBatches(String spreadsheetToken, String sheetId, 
                                                    String startCell, int totalRows, int totalColumns) {
        return readLargeDataInBatches(spreadsheetToken, sheetId, startCell, totalRows, totalColumns, 100);
    }
} 