2025-07-17 16:40:47.333  INFO 9428 --- [restartedMain] com.ruoyi.RuoYiApplication               : Starting RuoYiApplication using Java 21.0.7 on DESKTOP-BB9B7Q4 with PID 9428 (E:\java-project\backendAndFront\ruoyi-admin\target\classes started by admin in E:\java-project\backendAndFront)
2025-07-17 16:40:47.394 DEBUG 9428 --- [restartedMain] com.ruoyi.RuoYiApplication               : Running with Spring Boot v2.5.14, Spring v5.3.27
2025-07-17 16:40:47.394  INFO 9428 --- [restartedMain] com.ruoyi.RuoYiApplication               : The following 1 profile is active: "druid"
2025-07-17 16:40:54.111  INFO 9428 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-17 16:40:54.119  INFO 9428 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.102]
2025-07-17 16:40:54.162  INFO 9428 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-07-17 16:40:54.409 DEBUG 9428 --- [restartedMain] c.r.f.s.f.JwtAuthenticationTokenFilter   : Filter 'jwtAuthenticationTokenFilter' configured for use
2025-07-17 16:40:55.336  INFO 9428 --- [restartedMain] com.alibaba.druid.pool.DruidDataSource   : {dataSource-12} inited
2025-07-17 16:40:56.259 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectConfigList               : ==>  Preparing: select config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark from sys_config
2025-07-17 16:40:56.259 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectConfigList               : ==> Parameters: 
2025-07-17 16:40:56.270 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectConfigList               : <==      Total: 6
2025-07-17 16:40:56.747 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectDictDataList             : ==>  Preparing: select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark from sys_dict_data WHERE status = ? order by dict_sort asc
2025-07-17 16:40:56.748 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectDictDataList             : ==> Parameters: 0(String)
2025-07-17 16:40:56.761 DEBUG 9428 --- [restartedMain] c.r.s.m.S.selectDictDataList             : <==      Total: 33
2025-07-17 16:40:56.868  INFO 9428 --- [restartedMain] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.2 created.
2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-17 16:40:56.869  INFO 9428 --- [restartedMain] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.2
2025-07-17 16:40:56.870  INFO 9428 --- [restartedMain] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a3f1afe
2025-07-17 16:40:56.899 DEBUG 9428 --- [restartedMain] c.r.q.mapper.SysJobMapper.selectJobAll   : ==>  Preparing: select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark from sys_job
2025-07-17 16:40:56.899 DEBUG 9428 --- [restartedMain] c.r.q.mapper.SysJobMapper.selectJobAll   : ==> Parameters: 
2025-07-17 16:40:56.909 DEBUG 9428 --- [restartedMain] c.r.q.mapper.SysJobMapper.selectJobAll   : <==      Total: 5
2025-07-17 16:40:57.146  INFO 9428 --- [restartedMain] c.r.y.s.i.ProductListingInfoServiceImpl  : ProductListingInfoServiceImpl initialized with threadpool: coreSize=20, maxSize=50, queueCapacity=100, keepAliveSeconds=300
2025-07-17 16:40:57.148  INFO 9428 --- [restartedMain] c.r.y.s.i.ProductListingInfoServiceImpl  : RPA tasks will use priority class: low-priority
2025-07-17 16:40:57.203  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🏗️ 开始自动化构建责任链...
2025-07-17 16:40:57.284  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 📊 发现 23 个带注解的处理器
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 代销订单 -> distributionDxFormatProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 代销订单 -> distributionOrderMatchProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventoryDistributionFbaProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventoryDistributionWarehouseProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventoryFbaWarehouseProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventorySkuDimProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventoryTocWarehouseProcessor
2025-07-17 16:40:57.285 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 库存 -> inventoryWarehouseProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderCancelProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderEmptyValueProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderFxamOrderProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderFxpkPackageProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderSkuDimProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 订单 -> orderTradeReportProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 平台服务 -> platformServiceDxFormatProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 采购单 -> purchaseFxpkFormatProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 采购单 -> purchaseMainMatchProcessor
2025-07-17 16:40:57.286 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 采购单 -> purchaseOrderRouterProcessor
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 添加子链处理器: 采购单 -> sub_processor -> purchaseSubCodeHyphenProcessor
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 采购单 -> purchaseSubCodeParentProcessor
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 添加子链处理器: 采购单 -> sub_processor -> purchaseSubCodeUnderscoreProcessor
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 平台仓备货单 -> platformWarehouseProcessor
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 添加主链处理器: 平台仓备货单 -> warehouseStockOrderProcessor
2025-07-17 16:40:57.287  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [订单] 的责任链，包含 6 个处理器
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [5] orderFxpkPackageProcessor (order: 5, group: order-format)
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [6] orderFxamOrderProcessor (order: 6, group: order-format)
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [10] orderSkuDimProcessor (order: 10, group: matching)
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [20] orderTradeReportProcessor (order: 20, group: matching)
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [30] orderCancelProcessor (order: 30, group: special)
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [130] orderEmptyValueProcessor (order: 130, group: order-fallback)
2025-07-17 16:40:57.287  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [平台服务] 的责任链，包含 1 个处理器
2025-07-17 16:40:57.287 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [5] platformServiceDxFormatProcessor (order: 5, group: platformservice-dx)
2025-07-17 16:40:57.287  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [采购单] 的责任链，包含 4 个处理器
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [5] purchaseFxpkFormatProcessor (order: 5, group: purchase-fxpk)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [10] purchaseOrderRouterProcessor (order: 10, group: routing)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [20] purchaseMainMatchProcessor (order: 20, group: matching)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [30] purchaseSubCodeParentProcessor (order: 30, group: sub_code)
2025-07-17 16:40:57.288  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [代销订单] 的责任链，包含 2 个处理器
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [5] distributionDxFormatProcessor (order: 5, group: distribution-dx)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [30] distributionOrderMatchProcessor (order: 30, group: matching)
2025-07-17 16:40:57.288  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [平台仓备货单] 的责任链，包含 2 个处理器
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [10] platformWarehouseProcessor (order: 10, group: matching)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [20] warehouseStockOrderProcessor (order: 20, group: matching)
2025-07-17 16:40:57.288  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建交易类型 [库存] 的责任链，包含 6 个处理器
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [5] inventoryTocWarehouseProcessor (order: 5, group: inventory-toc)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [6] inventoryDistributionWarehouseProcessor (order: 6, group: inventory-distribution-warehouse)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [7] inventoryFbaWarehouseProcessor (order: 7, group: inventory-fba)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [8] inventoryDistributionFbaProcessor (order: 8, group: inventory-distribution-fba)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [10] inventorySkuDimProcessor (order: 10, group: matching)
2025-07-17 16:40:57.288 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ [20] inventoryWarehouseProcessor (order: 20, group: matching)
2025-07-17 16:40:57.289  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 🔗 构建子链 [采购单] -> [sub_processor]，包含 2 个处理器
2025-07-17 16:40:57.289 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ purchaseSubCodeHyphenProcessor (order: 31)
2025-07-17 16:40:57.289 DEBUG 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :    └─ purchaseSubCodeUnderscoreProcessor (order: 32)
2025-07-17 16:40:57.289  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : 📋 责任链构建结果报告:
2025-07-17 16:40:57.289  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 订单
2025-07-17 16:40:57.289  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. orderFxpkPackageProcessor (order: 5, group: order-format) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     2. orderFxamOrderProcessor (order: 6, group: order-format) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     3. orderSkuDimProcessor (order: 10, group: matching) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     4. orderTradeReportProcessor (order: 20, group: matching) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     5. orderCancelProcessor (order: 30, group: special) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     6. orderEmptyValueProcessor (order: 130, group: order-fallback)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 平台服务
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. platformServiceDxFormatProcessor (order: 5, group: platformservice-dx)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 采购单
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. purchaseFxpkFormatProcessor (order: 5, group: purchase-fxpk) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     2. purchaseOrderRouterProcessor (order: 10, group: routing) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     3. purchaseMainMatchProcessor (order: 20, group: matching) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     4. purchaseSubCodeParentProcessor (order: 30, group: sub_code)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 代销订单
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. distributionDxFormatProcessor (order: 5, group: distribution-dx) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     2. distributionOrderMatchProcessor (order: 30, group: matching)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 平台仓备货单
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. platformWarehouseProcessor (order: 10, group: matching) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     2. warehouseStockOrderProcessor (order: 20, group: matching)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🏷️ 主链 - 交易类型: 库存
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     1. inventoryTocWarehouseProcessor (order: 5, group: inventory-toc) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     2. inventoryDistributionWarehouseProcessor (order: 6, group: inventory-distribution-warehouse) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     3. inventoryFbaWarehouseProcessor (order: 7, group: inventory-fba) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     4. inventoryDistributionFbaProcessor (order: 8, group: inventory-distribution-fba) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     5. inventorySkuDimProcessor (order: 10, group: matching) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     6. inventoryWarehouseProcessor (order: 20, group: matching)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :   🔗 子链 - 交易类型: 采购单
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :     📦 分组: sub_processor
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :       1. purchaseSubCodeHyphenProcessor (order: 31) → 
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    :       2. purchaseSubCodeUnderscoreProcessor (order: 32)
2025-07-17 16:40:57.290  INFO 9428 --- [restartedMain] c.r.d.s.builder.ProcessorChainBuilder    : ✅ 责任链自动化构建完成，共构建 6 条责任链
2025-07-17 16:40:57.373  INFO 9428 --- [restartedMain] com.ruoyi.common.config.FeishuConfig     : 正在初始化飞书SDK客户端...
2025-07-17 16:40:57.374  INFO 9428 --- [restartedMain] com.ruoyi.common.config.FeishuConfig     : 飞书SDK客户端初始化成功 - AppId: cli_a8f89e71adfc100e
2025-07-17 16:40:57.733  INFO 9428 --- [restartedMain] c.r.data.service.impl.SyncStatusManager  : SyncStatusManager初始化完成，任务过期时间: 30分钟, 清理间隔: 10分钟
2025-07-17 16:41:00.362  INFO 9428 --- [restartedMain] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-17 16:41:00.366  INFO 9428 --- [restartedMain] com.ruoyi.RuoYiApplication               : Started RuoYiApplication in 13.216 seconds (JVM running for 1893.817)
2025-07-17 16:41:22.152  INFO 9428 --- [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 16:41:22.160  INFO 9428 --- [SpringApplicationShutdownHook] c.r.y.s.i.ProductListingInfoServiceImpl  : Shutting down RPA thread pool
2025-07-17 16:41:22.160  INFO 9428 --- [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-17 16:41:22.160  INFO 9428 --- [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-17 16:41:22.160  INFO 9428 --- [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-17 16:41:22.160  INFO 9428 --- [SpringApplicationShutdownHook] sys-user                                 : ====关闭后台任务任务线程池====
2025-07-17 16:41:22.161  WARN 9428 --- [SpringApplicationShutdownHook] .s.c.a.CommonAnnotationBeanPostProcessor : Destroy method on bean with name 'shutdownManager' threw an exception: java.lang.ExceptionInInitializerError
2025-07-17 16:41:22.163  INFO 9428 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-12} closing ...
2025-07-17 16:41:22.164  INFO 9428 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-12} closed
